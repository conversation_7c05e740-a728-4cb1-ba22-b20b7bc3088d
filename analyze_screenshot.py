#!/usr/bin/env python3
import cv2
import numpy as np

def analyze_game_area():
    # 读取游戏区域截图
    game_area = cv2.imread('current_game_area.png')
    if game_area is None:
        print('无法读取游戏区域截图')
        return

    print(f'游戏区域尺寸: {game_area.shape}')

    # 转换为HSV进行颜色分析
    hsv = cv2.cvtColor(game_area, cv2.COLOR_BGR2HSV)

    # 分析主要颜色
    # 检测蓝色背景
    blue_lower = np.array([100, 50, 50])
    blue_upper = np.array([130, 255, 255])
    blue_mask = cv2.inRange(hsv, blue_lower, blue_upper)
    blue_ratio = np.sum(blue_mask > 0) / (game_area.shape[0] * game_area.shape[1])

    # 检测黄色文字/按钮
    yellow_lower = np.array([20, 100, 100])
    yellow_upper = np.array([30, 255, 255])
    yellow_mask = cv2.inRange(hsv, yellow_lower, yellow_upper)
    yellow_ratio = np.sum(yellow_mask > 0) / (game_area.shape[0] * game_area.shape[1])

    # 检测白色文字
    gray = cv2.cvtColor(game_area, cv2.COLOR_BGR2GRAY)
    white_mask = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY)[1]
    white_ratio = np.sum(white_mask > 0) / (game_area.shape[0] * game_area.shape[1])

    print(f'蓝色比例: {blue_ratio:.3f}')
    print(f'黄色比例: {yellow_ratio:.3f}')
    print(f'白色比例: {white_ratio:.3f}')

    # 检测边缘和结构
    edges = cv2.Canny(gray, 50, 150)
    edge_density = np.sum(edges > 0) / edges.size
    print(f'边缘密度: {edge_density:.3f}')

    # 检测是否有游戏板的网格结构
    horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (20, 1))
    vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 20))

    horizontal_lines = cv2.morphologyEx(edges, cv2.MORPH_OPEN, horizontal_kernel)
    vertical_lines = cv2.morphologyEx(edges, cv2.MORPH_OPEN, vertical_kernel)

    h_line_ratio = np.sum(horizontal_lines > 0) / (game_area.shape[0] * game_area.shape[1])
    v_line_ratio = np.sum(vertical_lines > 0) / (game_area.shape[0] * game_area.shape[1])

    print(f'水平线比例: {h_line_ratio:.3f}')
    print(f'垂直线比例: {v_line_ratio:.3f}')

    # 检测方块（彩色且有一定大小的区域）
    saturation = hsv[:, :, 1]
    colorful_mask = cv2.threshold(saturation, 100, 255, cv2.THRESH_BINARY)[1]

    # 查找彩色区域的轮廓
    contours, _ = cv2.findContours(colorful_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    block_like_contours = 0
    for contour in contours:
        area = cv2.contourArea(contour)
        if 50 < area < 2000:  # 方块大小范围
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h if h > 0 else 0
            if 0.5 < aspect_ratio < 2.0:  # 接近正方形
                block_like_contours += 1

    print(f'方块轮廓数量: {block_like_contours}')

    # 判断游戏状态
    if blue_ratio > 0.3 and (yellow_ratio > 0.02 or white_ratio > 0.05):
        print('检测结果: 开始界面')
        return 'start_screen'
    elif ((h_line_ratio > 0.005 or v_line_ratio > 0.005) and block_like_contours >= 3):
        print('检测结果: 游戏进行中')
        return 'playing'
    else:
        print('检测结果: 未知状态')
        return 'unknown'

if __name__ == '__main__':
    analyze_game_area()
