# Tetris AI 配置文件

# 游戏窗口设置
GAME_REGION = (0, 80, 660, 940)  # (x, y, width, height)

# AI参数
ACTION_DELAY = 0.15  # 动作执行间隔（秒）
GAME_DURATION = 300  # 游戏时长（秒）

# 评估函数权重
EVALUATION_WEIGHTS = {
    'lines_cleared': 200,      # 消行奖励
    'height_penalty': 8,       # 高度惩罚基数
    'holes_penalty': 35,       # 洞穴惩罚
    'bumpiness_penalty': 6,    # 不平整惩罚
    'wells_penalty': 15,       # 深井惩罚
    'blocked_cells_penalty': 25,  # 被阻挡格子惩罚
    'row_transitions_penalty': 2,  # 行转换惩罚
    'col_transitions_penalty': 2,  # 列转换惩罚
    'tetris_bonus': 100,       # Tetris奖励
    'multi_line_bonus': 50     # 多行消除奖励
}

# 方块颜色检测范围 (HSV)
PIECE_COLORS = {
    'I': ([85, 80, 80], [95, 255, 255]),    # 青色
    'O': ([22, 80, 80], [28, 255, 255]),    # 黄色
    'T': ([145, 80, 80], [155, 255, 255]),  # 紫色
    'L': ([12, 80, 80], [18, 255, 255]),    # 橙色
    'J': ([115, 80, 80], [125, 255, 255]),  # 蓝色
    'S': ([45, 80, 80], [55, 255, 255]),    # 绿色
    'Z': ([2, 80, 80], [8, 255, 255])       # 红色
}

# 调试设置
DEBUG_SETTINGS = {
    'save_debug_images': True,
    'print_board_state': True,
    'log_actions': True,
    'performance_monitoring': True,
    'stats_interval': 30  # 统计信息打印间隔（秒）
}

# 性能设置
PERFORMANCE_SETTINGS = {
    'target_fps': 20,           # 目标帧率
    'max_frame_history': 100,   # 保留的帧时间历史数量
    'max_decision_history': 50, # 保留的决策时间历史数量
    'screenshot_timeout': 5     # 截图超时时间（秒）
}

# 游戏检测设置
DETECTION_SETTINGS = {
    'start_detection_methods': ['color', 'text', 'pattern'],
    'game_over_detection_methods': ['color', 'text', 'stillness'],
    'piece_detection_regions': 3,  # 检测区域数量
    'min_contour_area': 50,        # 最小轮廓面积
    'color_tolerance': 10          # 颜色容差
}
