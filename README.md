# Tetris AI for Game Boy Color

一个能够自动玩俄罗斯方块的AI程序，专为Game Boy Color模拟器设计。

## 功能特点

- 🎮 自动检测游戏开始界面并启动游戏
- 🧠 智能AI决策算法，综合考虑多种评估因素
- 📊 实时统计和性能监控
- 🔧 调试模式和可视化功能
- ⚡ 优化的屏幕捕获和图像处理
- 🎯 支持多种俄罗斯方块策略

## 安装要求

```bash
pip install -r requirements.txt
```

需要的依赖包：
- pyautogui==0.9.50
- opencv-python==*********
- numpy==2.3.1
- Pillow==10.4.0

## 使用方法

### 基本使用

1. 打开SameBoy模拟器并加载俄罗斯方块游戏
2. 将游戏窗口放在屏幕左上角
3. 运行AI：

```bash
python tetris_ai.py
```

### 高级选项

```bash
# 启用调试模式
python tetris_ai.py --debug

# 设置游戏时长（秒）
python tetris_ai.py --duration 600

# 自定义游戏窗口区域
python tetris_ai.py --region 0 80 660 940

# 组合使用
python tetris_ai.py --debug --duration 300 --region 0 80 660 940
```

## 配置说明

### 游戏窗口区域

默认游戏区域为 `(0, 80, 660, 940)`，表示：
- X坐标: 0
- Y坐标: 80  
- 宽度: 660
- 高度: 940

如果你的模拟器窗口位置不同，请使用 `--region` 参数调整。

### AI参数调整

在代码中可以调整以下参数：

- `action_delay`: 动作执行间隔（默认0.15秒）
- `game_duration`: 游戏时长（默认300秒）
- 评估函数权重：在 `evaluate_board()` 方法中调整

## AI策略

AI使用以下策略进行决策：

1. **多因素评估**：
   - 消除行数（正向奖励）
   - 堆积高度（负向惩罚）
   - 洞穴数量（负向惩罚）
   - 表面平整度（负向惩罚）
   - 深井数量（负向惩罚）

2. **动作优化**：
   - 支持旋转、左右移动、下落、硬下落
   - 动作队列系统，确保动作序列完整执行
   - 自适应延迟控制

3. **游戏状态检测**：
   - 多种方法检测游戏开始界面
   - 实时方块类型和位置识别
   - 游戏结束状态检测

## 调试功能

启用调试模式（`--debug`）后，AI会：

- 输出详细的运行日志
- 显示游戏板状态可视化
- 保存调试截图
- 统计性能数据
- 监控帧率和决策时间

## 故障排除

### 常见问题

1. **AI无法检测到游戏**
   - 检查游戏窗口位置是否正确
   - 尝试调整 `--region` 参数
   - 确保游戏画面清晰可见

2. **动作执行不准确**
   - 调整 `action_delay` 参数
   - 检查系统性能是否足够
   - 确保没有其他程序干扰

3. **截图失败**
   - 检查系统权限设置
   - 尝试重启模拟器
   - 确保屏幕区域没有被遮挡

### 性能优化

- 关闭不必要的后台程序
- 使用较低的屏幕分辨率
- 关闭调试模式以提高性能
- 调整游戏窗口大小

## 开发说明

### 代码结构

- `TetrisAI`: 主AI类
- `capture_screen()`: 屏幕捕获
- `detect_*()`: 各种检测方法
- `evaluate_board()`: 评估函数
- `get_best_move()`: 决策算法

### 扩展功能

可以通过以下方式扩展AI：

1. 添加新的评估因子
2. 改进方块检测算法
3. 优化动作执行策略
4. 添加机器学习组件

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
