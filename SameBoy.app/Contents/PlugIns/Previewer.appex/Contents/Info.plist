<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDisplayName</key>
	<string>Previewer</string>
	<key>CFBundleExecutable</key>
	<string>Previewer</string>
	<key>CFBundleIdentifier</key>
	<string>com.github.liji32.sameboy.ios.Previewer</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Previewer</string>
	<key>CFBundlePackageType</key>
	<string>XPC!</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0.1</string>
	<key>CFBundleSupportedPlatforms</key>
	<array>
		<string>MacOSX</string>
	</array>
	<key>LSMinimumSystemVersion</key>
	<string>12.0</string>
	<key>NSExtension</key>
	<dict>
		<key>NSExtensionAttributes</key>
		<dict>
			<key>QLSupportedContentTypes</key>
			<array>
				<string>com.github.liji32.sameboy.gb</string>
				<string>com.github.liji32.sameboy.gbc</string>
				<string>com.github.liji32.sameboy.isx</string>
				<string>public.gbrom</string>
			</array>
			<key>QLIsDataBasedPreview</key>
			<true/>
		</dict>
		<key>NSExtensionPointIdentifier</key>
		<string>com.apple.quicklook.preview</string>
		<key>NSExtensionPrincipalClass</key>
		<string>GBPreviewProvider</string>
	</dict>
</dict>
</plist>
