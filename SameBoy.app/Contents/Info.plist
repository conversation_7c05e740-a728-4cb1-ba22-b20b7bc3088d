<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDisplayName</key>
	<string>SameBoy</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>gb</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>Cartridge</string>
			<key>CFBundleTypeName</key>
			<string>Game Boy Game</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.github.liji32.sameboy.gb</string>
				<string>public.gbrom</string>
				<string>com.retroarch.gb</string>
			</array>
			<key>LSTypeIsPackage</key>
			<integer>0</integer>
			<key>NSDocumentClass</key>
			<string>Document</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>gbc</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>ColorCartridge</string>
			<key>CFBundleTypeName</key>
			<string>Game Boy Color Game</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.github.liji32.sameboy.gbc</string>
				<string>com.retroarch.gbc</string>
			</array>
			<key>LSTypeIsPackage</key>
			<integer>0</integer>
			<key>NSDocumentClass</key>
			<string>Document</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>isx</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>ColorCartridge</string>
			<key>CFBundleTypeName</key>
			<string>Game Boy ISX File</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.github.liji32.sameboy.isx</string>
			</array>
			<key>LSTypeIsPackage</key>
			<integer>0</integer>
			<key>NSDocumentClass</key>
			<string>Document</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>gbs</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>ColorCartridge</string>
			<key>CFBundleTypeName</key>
			<string>Game Boy Sound File</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.github.liji32.sameboy.gbs</string>
			</array>
			<key>LSTypeIsPackage</key>
			<integer>0</integer>
			<key>NSDocumentClass</key>
			<string>Document</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>gbcart</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>ColorCartridge</string>
			<key>CFBundleTypeName</key>
			<string>Game Boy Cartridge</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array/>
			<key>LSTypeIsPackage</key>
			<integer>1</integer>
			<key>NSDocumentClass</key>
			<string>Document</string>
		</dict>
	</array>
	<key>CFBundleExecutable</key>
	<string>SameBoy</string>
	<key>CFBundleIconFile</key>
	<string>AppIcon.icns</string>
	<key>CFBundleIdentifier</key>
	<string>com.github.liji32.sameboy</string>
	<key>LSApplicationCategoryType</key>
	<string>public.app-category.games</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>SameBoy</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0.1</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleSupportedPlatforms</key>
	<array>
		<string>MacOSX</string>
	</array>
	<key>LSMinimumSystemVersion</key>
	<string>10.9</string>
	<key>NSHumanReadableCopyright</key>
	<string>Copyright © 2015-2025 Lior Halphon</string>
	<key>NSMainNibFile</key>
	<string>MainMenu</string>
	<key>NSPrincipalClass</key>
	<string>GBApp</string>
	<key>UTExportedTypeDeclarations</key>
	<array>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.data</string>
			</array>
			<key>UTTypeDescription</key>
			<string>Game Boy Game</string>
			<key>UTTypeIconFile</key>
			<string>Cartridge</string>
			<key>UTTypeIdentifier</key>
			<string>com.github.liji32.sameboy.gb</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>gb</string>
				</array>
			</dict>
		</dict>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.data</string>
			</array>
			<key>UTTypeDescription</key>
			<string>Game Boy Color Game</string>
			<key>UTTypeIconFile</key>
			<string>ColorCartridge</string>
			<key>UTTypeIdentifier</key>
			<string>com.github.liji32.sameboy.gbc</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>gbc</string>
				</array>
			</dict>
		</dict>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.data</string>
			</array>
			<key>UTTypeDescription</key>
			<string>Game Boy ISX File</string>
			<key>UTTypeIconFile</key>
			<string>ColorCartridge</string>
			<key>UTTypeIdentifier</key>
			<string>com.github.liji32.sameboy.isx</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>isx</string>
				</array>
			</dict>
		</dict>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.data</string>
			</array>
			<key>UTTypeDescription</key>
			<string>Game Boy Sound File</string>
			<key>UTTypeIconFile</key>
			<string>ColorCartridge</string>
			<key>UTTypeIdentifier</key>
			<string>com.github.liji32.sameboy.gbs</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>gbs</string>
				</array>
			</dict>
		</dict>
	</array>
	<key>NSCameraUsageDescription</key>
	<string>SameBoy needs to access your device&apos;s camera to emulate the Game Boy Camera</string>
	<key>NSSupportsAutomaticGraphicsSwitching</key>
	<true/>
</dict>
</plist>
