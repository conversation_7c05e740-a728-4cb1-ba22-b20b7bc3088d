<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/CartridgeTemplate.png</key>
		<data>
		bGWjAItSNtEzs49/I+kcImPnjSU=
		</data>
		<key>Resources/ColorCartridgeTemplate.png</key>
		<data>
		OvZORAjaPkepyvqa+UfV34QUTw0=
		</data>
		<key>Resources/UniversalCartridgeTemplate.png</key>
		<data>
		CBgQonae8oCqKi40/Rq/TE2hUvA=
		</data>
		<key>Resources/cgb_boot_fast.bin</key>
		<data>
		JyJoBitKo6DFJiZZymm8NW7G4Zg=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>MacOS/SameBoy.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			AuAi76Sd9IRzsQGmxEOv9WKI5LE=
			</data>
			<key>requirement</key>
			<string>identifier SameBoy and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = V6QZC9P27F</string>
		</dict>
		<key>Resources/CartridgeTemplate.png</key>
		<dict>
			<key>hash</key>
			<data>
			bGWjAItSNtEzs49/I+kcImPnjSU=
			</data>
			<key>hash2</key>
			<data>
			2KRFyMvhWLFq/BHIvEmYnp4SfJ7MDO/35IosBQtT3e0=
			</data>
		</dict>
		<key>Resources/ColorCartridgeTemplate.png</key>
		<dict>
			<key>hash</key>
			<data>
			OvZORAjaPkepyvqa+UfV34QUTw0=
			</data>
			<key>hash2</key>
			<data>
			OUYY79B03oDc0HHG2Ci+ybkTZO4CQcBaF9qobdyjVP4=
			</data>
		</dict>
		<key>Resources/UniversalCartridgeTemplate.png</key>
		<dict>
			<key>hash</key>
			<data>
			CBgQonae8oCqKi40/Rq/TE2hUvA=
			</data>
			<key>hash2</key>
			<data>
			eQIXT6GDp9BnoqbO1f84HYpKMWQ1vguCsTPAQwFREYk=
			</data>
		</dict>
		<key>Resources/cgb_boot_fast.bin</key>
		<dict>
			<key>hash</key>
			<data>
			JyJoBitKo6DFJiZZymm8NW7G4Zg=
			</data>
			<key>hash2</key>
			<data>
			c0/HaFi8bXjnJN917+kBRsYSgX8Fxag3QJ+eo0xw4m4=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
