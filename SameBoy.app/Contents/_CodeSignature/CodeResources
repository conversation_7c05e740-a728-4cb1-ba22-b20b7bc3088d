<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/AppIcon.icns</key>
		<data>
		h1MZSS6l6HqmFmfUB14pYC5WDeo=
		</data>
		<key>Resources/AudioRecordingAccessoryView.nib/keyedobjects-101300.nib</key>
		<data>
		X9q4IkME96bgGbBXSEmv/icJltA=
		</data>
		<key>Resources/AudioRecordingAccessoryView.nib/keyedobjects.nib</key>
		<data>
		HVRHsndOWbiNzzD8iUh7sz9ujO8=
		</data>
		<key>Resources/BackstepTemplate.png</key>
		<data>
		BRAOGda7yck23eJzRa1nbVVDGx4=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		Z9jXLKMt7IGfoathiLnw1XsK3yA=
		</data>
		<key>Resources/CPU.png</key>
		<data>
		w/+5ZWPbnTzHOkBUcW7544HFUO0=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		G5tKh+oR1krrRmixliNMPk771VI=
		</data>
		<key>Resources/CPU~solid.png</key>
		<data>
		TiT72lw6x01+JMrFqjTOOnWWzj4=
		</data>
		<key>Resources/CPU~<EMAIL></key>
		<data>
		7u59CQ/EFEgluOdXgg8rWeyX0Uc=
		</data>
		<key>Resources/CPU~solid~dark.png</key>
		<data>
		clgOQBCNVCo0KACLTPWl1rcH6BQ=
		</data>
		<key>Resources/CPU~solid~<EMAIL></key>
		<data>
		M8tPiOAFqRxskhQy7ZoH3Uvcuo8=
		</data>
		<key>Resources/Cartridge.icns</key>
		<data>
		cp1rfrK02WxpK+dcJgoPbOI+ihY=
		</data>
		<key>Resources/CheatSearch.nib/keyedobjects-101300.nib</key>
		<data>
		Fvwu9ODrJK7W/6eg6ZV5cCpZzfo=
		</data>
		<key>Resources/CheatSearch.nib/keyedobjects.nib</key>
		<data>
		0huQ3qVYYP1LCb4mNREsA5G24II=
		</data>
		<key>Resources/ColorCartridge.icns</key>
		<data>
		wWFCCgmuEqANKKvipkQ9bTjrwII=
		</data>
		<key>Resources/ContinueTemplate.png</key>
		<data>
		tcHGmWgH9n7ysd7jwZwV0mJcmPA=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		D6PcWnraLHneEghkOHbMrUHtzpE=
		</data>
		<key>Resources/Credits.html</key>
		<data>
		fhbluxYerFc7HwBTnMtTGlHjJNU=
		</data>
		<key>Resources/Display.png</key>
		<data>
		UGWHXRo7/MLPb3tQFZk1jXu+F1E=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		Rg7d3pX7NcOj0mcc3lr6XBdsh2M=
		</data>
		<key>Resources/Display~solid.png</key>
		<data>
		4Ib1Uq3gIxxSKLuDkVye8IKiP20=
		</data>
		<key>Resources/Display~<EMAIL></key>
		<data>
		+EUO0aIUw6AzvTv2KizNbvAIj0Y=
		</data>
		<key>Resources/Display~solid~dark.png</key>
		<data>
		+M/dfwGbx2jR9cE/ICj3syAPgfk=
		</data>
		<key>Resources/Display~solid~<EMAIL></key>
		<data>
		2egXp9zYjzk5RGDgrCANHQAgzlU=
		</data>
		<key>Resources/Document.nib/keyedobjects-101300.nib</key>
		<data>
		FU8lcH10KJm+E+sGYWb2ghoXpyY=
		</data>
		<key>Resources/Document.nib/keyedobjects-110000.nib</key>
		<data>
		Qv1y7245nGijE4kmSDcdDX0KGTc=
		</data>
		<key>Resources/Document.nib/keyedobjects.nib</key>
		<data>
		gP807Jt9CVxfI9f3YhpY++fiTAE=
		</data>
		<key>Resources/FinishTemplate.png</key>
		<data>
		VB2O4HU71RuY1twBSWlrzYhruQM=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		fa8kvdT5vIJ7WNYWVwNayNCa3lo=
		</data>
		<key>Resources/GBObjectViewItem.nib/keyedobjects-101300.nib</key>
		<data>
		wm6+mxwVTwGVkyiuoILEih5cM+A=
		</data>
		<key>Resources/GBObjectViewItem.nib/keyedobjects.nib</key>
		<data>
		W5EmRuwFvJbvNFKkcYFjjpzBRDM=
		</data>
		<key>Resources/GBPaletteViewRow.nib/keyedobjects-101300.nib</key>
		<data>
		0/5f4T2JH919ukZpr2WQ+JnOPhw=
		</data>
		<key>Resources/GBPaletteViewRow.nib/keyedobjects.nib</key>
		<data>
		rS06zk68HEFqbQqNCjKtoMzI7r0=
		</data>
		<key>Resources/GBS.nib/keyedobjects-101300.nib</key>
		<data>
		GsO1pZfOThI6Zf9p9Uw837vaoQU=
		</data>
		<key>Resources/GBS.nib/keyedobjects.nib</key>
		<data>
		q2qziUrAznCAWo4+Wu5EP0Os0PI=
		</data>
		<key>Resources/GBS11.nib/keyedobjects-101300.nib</key>
		<data>
		c+9I/K/oNhEjo5tnWcloHqByqHw=
		</data>
		<key>Resources/GBS11.nib/keyedobjects.nib</key>
		<data>
		TrqM/TRVRufWf00nOo+P++Gt6P0=
		</data>
		<key>Resources/HelpTemplate.png</key>
		<data>
		uQCc5niJRJh40URjLWAL2d+lqVA=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		d3A/7YqDTIe1611nGjDwrK0oRPE=
		</data>
		<key>Resources/HorizontalJoyConLeftTemplate.png</key>
		<data>
		Fp8sdZqeQnWKRYJyfOP5kGoIBt8=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		968EUq80aIQDnAd3xPVlfbf/X1U=
		</data>
		<key>Resources/HorizontalJoyConRightTemplate.png</key>
		<data>
		SmUslDLp0BBO1jebc0MhZonWDj8=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		FclbSS7S1QBYJBRmgM+nEeadRUY=
		</data>
		<key>Resources/Icon.png</key>
		<data>
		PaXS8p5Z+jSWVk/yb1rNDdZMqJ8=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		uB809vHmg0WJKozHcK4Lk6PUt2g=
		</data>
		<key>Resources/InterruptTemplate.png</key>
		<data>
		U416kJFqeffEEnN+UHR87uV72F0=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		EtWR1h4skW3fAPOxXIu4FLCtrXk=
		</data>
		<key>Resources/JoyConDualTemplate.png</key>
		<data>
		aPFMamJhSECRQk1Rggj/A1VeKvo=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		BmYqx0zglOKJdMEuOMqTRjGQm4Q=
		</data>
		<key>Resources/JoyConLeftTemplate.png</key>
		<data>
		QhNVLLWCGIZ/d+izTgfcP7yF4RI=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		0hp2cqr5dH5JraENaA0AsI2gjHA=
		</data>
		<key>Resources/JoyConRightTemplate.png</key>
		<data>
		1SKjjYro/37WoZT8ugv85ZNphuc=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		MnA7mpfVg4+z8N7DLtY1TNoF0xk=
		</data>
		<key>Resources/Joypad.png</key>
		<data>
		QiaxEKNGrneM98wTiEACDEwNcyc=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		H7bgoff5bxuIMk5xnSRoRhyBfDc=
		</data>
		<key>Resources/Joypad~dark.png</key>
		<data>
		wGy4sxvaQyEQ+/tBCP2TH3weVsI=
		</data>
		<key>Resources/Joypad~<EMAIL></key>
		<data>
		x0DYCuQgxZrWQhW1gRmJlSV/U3w=
		</data>
		<key>Resources/Joypad~solid.png</key>
		<data>
		KwPpRrrHWkhvnTonMe0jqfoQnMk=
		</data>
		<key>Resources/Joypad~<EMAIL></key>
		<data>
		h6+UnewKt+h+CNz74HNo6Zif9is=
		</data>
		<key>Resources/MainMenu.nib/keyedobjects-101300.nib</key>
		<data>
		lvFK5XiQswbEF+zZTbAwE0ojEo0=
		</data>
		<key>Resources/MainMenu.nib/keyedobjects.nib</key>
		<data>
		6Hg/kbqRR4S4XsSWrEivW3OqLpE=
		</data>
		<key>Resources/Next.png</key>
		<data>
		dW/Nuxb3BcG64ZE+FNOL6ueg8gA=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		Np4OouHxRPtU9N5xLrWU4gabhVs=
		</data>
		<key>Resources/NextTemplate.png</key>
		<data>
		SuksKCSwxrOo/8pqN2xzGbLx6/o=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		YfgIwuaKQ7kAzHTEZC3Apzxpsrk=
		</data>
		<key>Resources/Pause.png</key>
		<data>
		8hcOOEVixHrdZ63BFi8PJHoQUBY=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		7PPS3ek4OlkJl9nq0zwTsP/U1kY=
		</data>
		<key>Resources/Play.png</key>
		<data>
		JzEAOCLSBtSyKjtyU9r9f7tv8c4=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		Xzqf/n0SSKcObhRk/1o9m5rKEek=
		</data>
		<key>Resources/PopoverView.nib/keyedobjects-101300.nib</key>
		<data>
		tZ8p0PxF/ptR14tdr6wSIo9UNj0=
		</data>
		<key>Resources/PopoverView.nib/keyedobjects.nib</key>
		<data>
		LlWwNSGK+MZrnAgqBmYlfmVJ9Gk=
		</data>
		<key>Resources/Preferences.nib/keyedobjects-101300.nib</key>
		<data>
		6yMFQtkgrnO17r7SNRvheXUqjhE=
		</data>
		<key>Resources/Preferences.nib/keyedobjects-110000.nib</key>
		<data>
		ccyKkFnYqiH0g1U7a+yqurFLzq0=
		</data>
		<key>Resources/Preferences.nib/keyedobjects.nib</key>
		<data>
		BJK4LmqCs76MG8+f8vYLNJiEG3E=
		</data>
		<key>Resources/Previous.png</key>
		<data>
		w3SFZP5T1FSm46FcxbYzl3xAKx8=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		ORTopSE+Py/5AUdAVaEW5LuR+nk=
		</data>
		<key>Resources/Rewind.png</key>
		<data>
		YnDGAepfKpgmWrgihg5Y2PxUOjc=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		V99AciTISZjDiqkbAjakPJC7YFw=
		</data>
		<key>Resources/Shaders/AAOmniScaleLegacy.fsh</key>
		<data>
		3wXKCc5VFd8Nl5uDgChdiEk2lb4=
		</data>
		<key>Resources/Shaders/AAScale2x.fsh</key>
		<data>
		8ZUYkTfhZVij7sq6ei39RV8ib6Q=
		</data>
		<key>Resources/Shaders/AAScale4x.fsh</key>
		<data>
		2JKgPCvqwwG4cKtof+UwNxrHTZo=
		</data>
		<key>Resources/Shaders/Bilinear.fsh</key>
		<data>
		rm3VBA/I4xGPJzIKQESWW/XIE9I=
		</data>
		<key>Resources/Shaders/CRT.fsh</key>
		<data>
		N0xzNk8kyb6yEilzTwozLSZ9O1k=
		</data>
		<key>Resources/Shaders/FlatCRT.fsh</key>
		<data>
		LJ2BWFeIT98jo1O6vtX2FrzNR1o=
		</data>
		<key>Resources/Shaders/HQ2x.fsh</key>
		<data>
		dhQGj7kbG4A2/n+UoG9nQ2TguR4=
		</data>
		<key>Resources/Shaders/LCD.fsh</key>
		<data>
		LJKNG9JVWsaaDD3Ysa5ELwYHUng=
		</data>
		<key>Resources/Shaders/MasterShader.fsh</key>
		<data>
		nLUUqGvJz22njMkw0Zl2Z4aJJ8A=
		</data>
		<key>Resources/Shaders/MasterShader.metal</key>
		<data>
		T/28d6orNIuR1FGAI7VUnRZQqVM=
		</data>
		<key>Resources/Shaders/MonoLCD.fsh</key>
		<data>
		aLIJP6SxX3iY2OYLu+K01pNLAko=
		</data>
		<key>Resources/Shaders/NearestNeighbor.fsh</key>
		<data>
		u+KfKwz6i5NWFtTql0EsZUjoHU4=
		</data>
		<key>Resources/Shaders/OmniScale.fsh</key>
		<data>
		GJoOSXJrm00rPOzmcz9wiPrHlJ0=
		</data>
		<key>Resources/Shaders/OmniScaleLegacy.fsh</key>
		<data>
		nmly441l/ivqXu7+tYQkhrDRbYg=
		</data>
		<key>Resources/Shaders/Scale2x.fsh</key>
		<data>
		Y4iop9rQCc79m8fX48BS1Tt+YZU=
		</data>
		<key>Resources/Shaders/Scale4x.fsh</key>
		<data>
		JTj1y9WMMdb1eF8tXGcUnIfVuaw=
		</data>
		<key>Resources/Shaders/SmoothBilinear.fsh</key>
		<data>
		KVBM1SDpVH4FGt+hczgkH7JY5OU=
		</data>
		<key>Resources/Speaker.png</key>
		<data>
		eqy6nJ31oK86gm0ilOWtN6ToefU=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		EYpo+FkCcXHJC4cNlCsBWVU098Y=
		</data>
		<key>Resources/Speaker~dark.png</key>
		<data>
		lOo44KAyCnp44cL6tL/LE5tjIbo=
		</data>
		<key>Resources/Speaker~<EMAIL></key>
		<data>
		isy7doI7LBUA88TQefUQoZMnT0I=
		</data>
		<key>Resources/Speaker~solid.png</key>
		<data>
		0E53fybX93ODjXVnHf1Cwdxyed8=
		</data>
		<key>Resources/Speaker~<EMAIL></key>
		<data>
		/aSAtsQ8trsxaotU2gMDj2TWnHU=
		</data>
		<key>Resources/StepTemplate.png</key>
		<data>
		fis/htCuJtN40Fx8NEhd+fqCa/0=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		9CcW5+zJuwtEAQ3N3eCHjklw3Vs=
		</data>
		<key>Resources/UpdateWindow.nib/keyedobjects-101300.nib</key>
		<data>
		mWegjJ2IUPEeC+S3QKirsxpnWz8=
		</data>
		<key>Resources/UpdateWindow.nib/keyedobjects.nib</key>
		<data>
		ToCsFQFKc31E39GITi+AxgmcJUY=
		</data>
		<key>Resources/Updates.png</key>
		<data>
		WKdJcKdI+Lw9OgTl2gjXp292WSE=
		</data>
		<key>Resources/<EMAIL></key>
		<data>
		PaXS8p5Z+jSWVk/yb1rNDdZMqJ8=
		</data>
		<key>Resources/Updates~solid.png</key>
		<data>
		ut525kIdWlKumYWm64JZp/7N6J4=
		</data>
		<key>Resources/Updates~<EMAIL></key>
		<data>
		YGtQYKaHHWk8RxQEIG4gASInvY0=
		</data>
		<key>Resources/Updates~solid~dark.png</key>
		<data>
		DGqfgvFLelZV3HnhgyQqMgMf6iM=
		</data>
		<key>Resources/Updates~solid~<EMAIL></key>
		<data>
		PnClHyWPa5TkJIoJx2qWd11II/c=
		</data>
		<key>Resources/agb_boot.bin</key>
		<data>
		TtHZXJxoKnhpjyNc6em7O2K8z58=
		</data>
		<key>Resources/cgb0_boot.bin</key>
		<data>
		gico++HWN1KMV2Wa2G4L9dpmZnc=
		</data>
		<key>Resources/cgb_boot.bin</key>
		<data>
		ETkDd1qdNLeYwvgHZnLaZiaBWpE=
		</data>
		<key>Resources/dmg_boot.bin</key>
		<data>
		HbV6HotuQJb4EVh/nqsMZnX9l1U=
		</data>
		<key>Resources/mgb_boot.bin</key>
		<data>
		mBe9rpM1o6zLWE93wWXcVyZVXR8=
		</data>
		<key>Resources/registers.sym</key>
		<data>
		ud/QDbrHIhKPjlp7ROVCnIrP+do=
		</data>
		<key>Resources/sgb2_boot.bin</key>
		<data>
		8oKzqvmPhCPat9d/GqAZK+Yw8vs=
		</data>
		<key>Resources/sgb_boot.bin</key>
		<data>
		Np5uteDJdeqlKko/buB7Kjw8Ft4=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Library/QuickLook/SameBoy.qlgenerator/Contents/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			Hkt2K6n+8DGzKoyi6okAyUtu3Zk=
			</data>
			<key>hash2</key>
			<data>
			SPAMT9oqOKaGa+eWYGTU4QXhSMzfAnxKF3oGljq8su8=
			</data>
		</dict>
		<key>Library/QuickLook/SameBoy.qlgenerator/Contents/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			ipiJy+FoPQTqMIeHIdPXjL0ITC8=
			</data>
			<key>hash2</key>
			<data>
			dHDuMIyn1r3zgcAmCd8eW9Wlhg2ET1XVWIGxL36BOhQ=
			</data>
		</dict>
		<key>Library/QuickLook/SameBoy.qlgenerator/Contents/MacOS/SameBoy.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			/BhRcewvvozY83EuLDcpiiWeJAk=
			</data>
			<key>hash2</key>
			<data>
			1oFI2r5J5SStSfHbZD1b/ly9XBaz+9VAkdD+HZ/a9Bg=
			</data>
		</dict>
		<key>Library/QuickLook/SameBoy.qlgenerator/Contents/MacOS/SameBoyQL</key>
		<dict>
			<key>hash</key>
			<data>
			SpB7cscV7fU8IVbRQlJUaJ6lFV4=
			</data>
			<key>hash2</key>
			<data>
			BKcZ4bsoCV6bnNACIgu9diJ99ZURL/CXJ55QDsndiYA=
			</data>
		</dict>
		<key>Library/QuickLook/SameBoy.qlgenerator/Contents/Resources/CartridgeTemplate.png</key>
		<dict>
			<key>hash</key>
			<data>
			bGWjAItSNtEzs49/I+kcImPnjSU=
			</data>
			<key>hash2</key>
			<data>
			2KRFyMvhWLFq/BHIvEmYnp4SfJ7MDO/35IosBQtT3e0=
			</data>
		</dict>
		<key>Library/QuickLook/SameBoy.qlgenerator/Contents/Resources/ColorCartridgeTemplate.png</key>
		<dict>
			<key>hash</key>
			<data>
			OvZORAjaPkepyvqa+UfV34QUTw0=
			</data>
			<key>hash2</key>
			<data>
			OUYY79B03oDc0HHG2Ci+ybkTZO4CQcBaF9qobdyjVP4=
			</data>
		</dict>
		<key>Library/QuickLook/SameBoy.qlgenerator/Contents/Resources/UniversalCartridgeTemplate.png</key>
		<dict>
			<key>hash</key>
			<data>
			CBgQonae8oCqKi40/Rq/TE2hUvA=
			</data>
			<key>hash2</key>
			<data>
			eQIXT6GDp9BnoqbO1f84HYpKMWQ1vguCsTPAQwFREYk=
			</data>
		</dict>
		<key>Library/QuickLook/SameBoy.qlgenerator/Contents/Resources/cgb_boot_fast.bin</key>
		<dict>
			<key>hash</key>
			<data>
			JyJoBitKo6DFJiZZymm8NW7G4Zg=
			</data>
			<key>hash2</key>
			<data>
			c0/HaFi8bXjnJN917+kBRsYSgX8Fxag3QJ+eo0xw4m4=
			</data>
		</dict>
		<key>Library/QuickLook/SameBoy.qlgenerator/Contents/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			5zmdnKLn8nbFEHRej2O/DVrg+Pw=
			</data>
			<key>hash2</key>
			<data>
			iwtOqN8gtYK+jVNiueK7001Gdo56DoU0LYWPRnZR1RE=
			</data>
		</dict>
		<key>PlugIns/Previewer.appex</key>
		<dict>
			<key>cdhash</key>
			<data>
			4k/gvmMLTt+bV4rZck23d0sljEA=
			</data>
			<key>requirement</key>
			<string>identifier "com.github.liji32.sameboy.ios.Previewer" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = V6QZC9P27F</string>
		</dict>
		<key>PlugIns/Thumbnailer.appex</key>
		<dict>
			<key>cdhash</key>
			<data>
			N37ZZ0y64aQbVpr+TjjtJNAZ+WQ=
			</data>
			<key>requirement</key>
			<string>identifier "com.github.liji32.sameboy.ios.Thumbnailer" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = V6QZC9P27F</string>
		</dict>
		<key>Resources/AppIcon.icns</key>
		<dict>
			<key>hash</key>
			<data>
			h1MZSS6l6HqmFmfUB14pYC5WDeo=
			</data>
			<key>hash2</key>
			<data>
			wMSWxe+Q4TU5ttD1kz19XBKFhQRxCkoORTAvxBy+mrY=
			</data>
		</dict>
		<key>Resources/AudioRecordingAccessoryView.nib/keyedobjects-101300.nib</key>
		<dict>
			<key>hash</key>
			<data>
			X9q4IkME96bgGbBXSEmv/icJltA=
			</data>
			<key>hash2</key>
			<data>
			O9N3aSfFE3B8U3AxxGhBqd/Vco+7xH68gpCrNacNiMA=
			</data>
		</dict>
		<key>Resources/AudioRecordingAccessoryView.nib/keyedobjects.nib</key>
		<dict>
			<key>hash</key>
			<data>
			HVRHsndOWbiNzzD8iUh7sz9ujO8=
			</data>
			<key>hash2</key>
			<data>
			n3qdKLqVVnxZrYuGpGksDLNNlebIvYSxvYtIXG4Pa0g=
			</data>
		</dict>
		<key>Resources/BackstepTemplate.png</key>
		<dict>
			<key>hash</key>
			<data>
			BRAOGda7yck23eJzRa1nbVVDGx4=
			</data>
			<key>hash2</key>
			<data>
			u910K0nJpArhbnaaDJSFKuyVx/DAiO00mZyEU59lSnk=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			Z9jXLKMt7IGfoathiLnw1XsK3yA=
			</data>
			<key>hash2</key>
			<data>
			fdyqM2JWdZ74kN7enUAZyREsq0ZYLldmVBH8CaNNQkc=
			</data>
		</dict>
		<key>Resources/CPU.png</key>
		<dict>
			<key>hash</key>
			<data>
			w/+5ZWPbnTzHOkBUcW7544HFUO0=
			</data>
			<key>hash2</key>
			<data>
			+iChQ+bAdYcqPUYU7u0P5GCpVsN7MUF6yaUr96FcfkM=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			G5tKh+oR1krrRmixliNMPk771VI=
			</data>
			<key>hash2</key>
			<data>
			O9i5j8NqJiMZ4eOH0Z9nTrgiMZBEobFLq7eJAEaLqrc=
			</data>
		</dict>
		<key>Resources/CPU~solid.png</key>
		<dict>
			<key>hash</key>
			<data>
			TiT72lw6x01+JMrFqjTOOnWWzj4=
			</data>
			<key>hash2</key>
			<data>
			jwQQ0tx9vi8apxtnv8MKqjLyno2lXCUDl2YMfAeQcSo=
			</data>
		</dict>
		<key>Resources/CPU~<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			7u59CQ/EFEgluOdXgg8rWeyX0Uc=
			</data>
			<key>hash2</key>
			<data>
			E1UvzM33AgyRbbRrxKRz6/vTarOBY1uxR+BObWz+xAs=
			</data>
		</dict>
		<key>Resources/CPU~solid~dark.png</key>
		<dict>
			<key>hash</key>
			<data>
			clgOQBCNVCo0KACLTPWl1rcH6BQ=
			</data>
			<key>hash2</key>
			<data>
			GOef/fql+6EKuiRKLdrwTGLzuewoPY7VBomK9tuIdLg=
			</data>
		</dict>
		<key>Resources/CPU~solid~<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			M8tPiOAFqRxskhQy7ZoH3Uvcuo8=
			</data>
			<key>hash2</key>
			<data>
			fsiz3lxFOflWFq52xOiXj3ghdSb0PfwbkKSb7l5SNgU=
			</data>
		</dict>
		<key>Resources/Cartridge.icns</key>
		<dict>
			<key>hash</key>
			<data>
			cp1rfrK02WxpK+dcJgoPbOI+ihY=
			</data>
			<key>hash2</key>
			<data>
			Ki7QXd1/kFraX7zhthDcaqYyq4sGEGKIX4uTCSECysQ=
			</data>
		</dict>
		<key>Resources/CheatSearch.nib/keyedobjects-101300.nib</key>
		<dict>
			<key>hash</key>
			<data>
			Fvwu9ODrJK7W/6eg6ZV5cCpZzfo=
			</data>
			<key>hash2</key>
			<data>
			Dg2OCKfOgoa0hckFXl2jKqxCXeS1sQURfGVSuorvXEs=
			</data>
		</dict>
		<key>Resources/CheatSearch.nib/keyedobjects.nib</key>
		<dict>
			<key>hash</key>
			<data>
			0huQ3qVYYP1LCb4mNREsA5G24II=
			</data>
			<key>hash2</key>
			<data>
			ZQTFVZ4Moe9KPqA1H6gt0aIQMYocdQYxjKztzPwWyDM=
			</data>
		</dict>
		<key>Resources/ColorCartridge.icns</key>
		<dict>
			<key>hash</key>
			<data>
			wWFCCgmuEqANKKvipkQ9bTjrwII=
			</data>
			<key>hash2</key>
			<data>
			+/k2Ebj1Qm01kC7p/F0MptFGdE2WpLwTuPKDMDZTg2U=
			</data>
		</dict>
		<key>Resources/ContinueTemplate.png</key>
		<dict>
			<key>hash</key>
			<data>
			tcHGmWgH9n7ysd7jwZwV0mJcmPA=
			</data>
			<key>hash2</key>
			<data>
			LCasMgmvP58fkHUlwzWYcOKq4wyqrFjG1TKm9mDUbDY=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			D6PcWnraLHneEghkOHbMrUHtzpE=
			</data>
			<key>hash2</key>
			<data>
			x+igWzyX6l8MOoGtZpopR+7hRD2sljycu4C0nD0nPgc=
			</data>
		</dict>
		<key>Resources/Credits.html</key>
		<dict>
			<key>hash</key>
			<data>
			fhbluxYerFc7HwBTnMtTGlHjJNU=
			</data>
			<key>hash2</key>
			<data>
			tqXu7WzuJSezM5mWLBq+XrJWkRGmX95NG4+EKli9e+A=
			</data>
		</dict>
		<key>Resources/Display.png</key>
		<dict>
			<key>hash</key>
			<data>
			UGWHXRo7/MLPb3tQFZk1jXu+F1E=
			</data>
			<key>hash2</key>
			<data>
			5TN2XuR0UYoYg8PGczwA//JC8P67X2avxnQI7XAJqUE=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			Rg7d3pX7NcOj0mcc3lr6XBdsh2M=
			</data>
			<key>hash2</key>
			<data>
			IaUp7iO0Eftg4BBilNCWgub6KUfqT/ASFB+ZqbUs0vY=
			</data>
		</dict>
		<key>Resources/Display~solid.png</key>
		<dict>
			<key>hash</key>
			<data>
			4Ib1Uq3gIxxSKLuDkVye8IKiP20=
			</data>
			<key>hash2</key>
			<data>
			xKk1awvwhv0Lnba5OII9AQwUrTSOYNn7Oq/sIMV3GiE=
			</data>
		</dict>
		<key>Resources/Display~<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			+EUO0aIUw6AzvTv2KizNbvAIj0Y=
			</data>
			<key>hash2</key>
			<data>
			SkY8jRA/L62iI/VMiuULAs70s0FvzkmsAoKu5x+2gVY=
			</data>
		</dict>
		<key>Resources/Display~solid~dark.png</key>
		<dict>
			<key>hash</key>
			<data>
			+M/dfwGbx2jR9cE/ICj3syAPgfk=
			</data>
			<key>hash2</key>
			<data>
			5rU/Z1TpFWXRAr+XnrYlJLPosUvTdDKbe7D2XJTCVX8=
			</data>
		</dict>
		<key>Resources/Display~solid~<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			2egXp9zYjzk5RGDgrCANHQAgzlU=
			</data>
			<key>hash2</key>
			<data>
			ktgtizG1vMj566M0ZtzT5/LzJtTzUUxV34aM5+fe5es=
			</data>
		</dict>
		<key>Resources/Document.nib/keyedobjects-101300.nib</key>
		<dict>
			<key>hash</key>
			<data>
			FU8lcH10KJm+E+sGYWb2ghoXpyY=
			</data>
			<key>hash2</key>
			<data>
			rit9QzqFHD+g+DOlz2th1ixZXR6btkKWZsOZX777cC8=
			</data>
		</dict>
		<key>Resources/Document.nib/keyedobjects-110000.nib</key>
		<dict>
			<key>hash</key>
			<data>
			Qv1y7245nGijE4kmSDcdDX0KGTc=
			</data>
			<key>hash2</key>
			<data>
			XoYzIEQRpEdPySYzjmZuVwSDUe2AsyUvk+C5n23Lr1Q=
			</data>
		</dict>
		<key>Resources/Document.nib/keyedobjects.nib</key>
		<dict>
			<key>hash</key>
			<data>
			gP807Jt9CVxfI9f3YhpY++fiTAE=
			</data>
			<key>hash2</key>
			<data>
			LdKwBP927yqs6HUccwLRXRIsUSmo/jXZP6KBFH61AFQ=
			</data>
		</dict>
		<key>Resources/FinishTemplate.png</key>
		<dict>
			<key>hash</key>
			<data>
			VB2O4HU71RuY1twBSWlrzYhruQM=
			</data>
			<key>hash2</key>
			<data>
			3DIUacVBr/XeT//x1alFVN25j2QSGv5zI0cKZWetLvc=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			fa8kvdT5vIJ7WNYWVwNayNCa3lo=
			</data>
			<key>hash2</key>
			<data>
			ZQvJp1QJtnR4JJQ4JYeib4O2jKtF/8BBnjj4CCFNbWM=
			</data>
		</dict>
		<key>Resources/GBObjectViewItem.nib/keyedobjects-101300.nib</key>
		<dict>
			<key>hash</key>
			<data>
			wm6+mxwVTwGVkyiuoILEih5cM+A=
			</data>
			<key>hash2</key>
			<data>
			IV4oPPfScajnihrzma0u7DZvWRJ/8GxvCY42xBqZeT4=
			</data>
		</dict>
		<key>Resources/GBObjectViewItem.nib/keyedobjects.nib</key>
		<dict>
			<key>hash</key>
			<data>
			W5EmRuwFvJbvNFKkcYFjjpzBRDM=
			</data>
			<key>hash2</key>
			<data>
			Cbj8x7/PWWm4BR7TLkTqB3Mpi1gRD1dOSQzGj4YjWEA=
			</data>
		</dict>
		<key>Resources/GBPaletteViewRow.nib/keyedobjects-101300.nib</key>
		<dict>
			<key>hash</key>
			<data>
			0/5f4T2JH919ukZpr2WQ+JnOPhw=
			</data>
			<key>hash2</key>
			<data>
			DpGwNRmfUm6ndSJ1xf70JwJbsnk3AdIr2G1SOYvRFhk=
			</data>
		</dict>
		<key>Resources/GBPaletteViewRow.nib/keyedobjects.nib</key>
		<dict>
			<key>hash</key>
			<data>
			rS06zk68HEFqbQqNCjKtoMzI7r0=
			</data>
			<key>hash2</key>
			<data>
			lzE+uqHovwMOjFQEvTyYNaX5o5ARjEyyzIDB+IfhZq8=
			</data>
		</dict>
		<key>Resources/GBS.nib/keyedobjects-101300.nib</key>
		<dict>
			<key>hash</key>
			<data>
			GsO1pZfOThI6Zf9p9Uw837vaoQU=
			</data>
			<key>hash2</key>
			<data>
			YQgtYvOOB/i9WgIGcl0m5DAGEpyX7q2vcv4NzxQ135Q=
			</data>
		</dict>
		<key>Resources/GBS.nib/keyedobjects.nib</key>
		<dict>
			<key>hash</key>
			<data>
			q2qziUrAznCAWo4+Wu5EP0Os0PI=
			</data>
			<key>hash2</key>
			<data>
			DesEmc0S8I9Y/wMHPVI7GvSPkLZicu7uZgvu7cjq6f4=
			</data>
		</dict>
		<key>Resources/GBS11.nib/keyedobjects-101300.nib</key>
		<dict>
			<key>hash</key>
			<data>
			c+9I/K/oNhEjo5tnWcloHqByqHw=
			</data>
			<key>hash2</key>
			<data>
			+OhAtKWv+ZVNf8LHfRPmLI903UFX5jR4Y5rkMsCtoJc=
			</data>
		</dict>
		<key>Resources/GBS11.nib/keyedobjects.nib</key>
		<dict>
			<key>hash</key>
			<data>
			TrqM/TRVRufWf00nOo+P++Gt6P0=
			</data>
			<key>hash2</key>
			<data>
			EAb14G+XZgi8zLvNIpr047EqWI/KANg0OdvxN7ZVokc=
			</data>
		</dict>
		<key>Resources/HelpTemplate.png</key>
		<dict>
			<key>hash</key>
			<data>
			uQCc5niJRJh40URjLWAL2d+lqVA=
			</data>
			<key>hash2</key>
			<data>
			BeiZANf2thFzgXg32XYAlNiGfbYRRrVwzKPi012New8=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			d3A/7YqDTIe1611nGjDwrK0oRPE=
			</data>
			<key>hash2</key>
			<data>
			XM3bUvFWCNF0I/vTbI5+Z3sJUdP2kRI+FAgJYyb+xQ0=
			</data>
		</dict>
		<key>Resources/HorizontalJoyConLeftTemplate.png</key>
		<dict>
			<key>hash</key>
			<data>
			Fp8sdZqeQnWKRYJyfOP5kGoIBt8=
			</data>
			<key>hash2</key>
			<data>
			U9JiFw3rg5PVLd3ndN2cyGtKAAo5u+1TGMMtm6rzFJY=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			968EUq80aIQDnAd3xPVlfbf/X1U=
			</data>
			<key>hash2</key>
			<data>
			JM09aB95WRXHjcF73xPJ4PG+tDGyQXyc9ZZP7mR66yw=
			</data>
		</dict>
		<key>Resources/HorizontalJoyConRightTemplate.png</key>
		<dict>
			<key>hash</key>
			<data>
			SmUslDLp0BBO1jebc0MhZonWDj8=
			</data>
			<key>hash2</key>
			<data>
			zuH6ZoDzkLricfMd8YlaLogtn+WzotDizdN8l0smVyI=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			FclbSS7S1QBYJBRmgM+nEeadRUY=
			</data>
			<key>hash2</key>
			<data>
			uEIAg1q15Wxj1G+bWHlHEOFJf1lywyFiWpGZ7qZk95g=
			</data>
		</dict>
		<key>Resources/Icon.png</key>
		<dict>
			<key>hash</key>
			<data>
			PaXS8p5Z+jSWVk/yb1rNDdZMqJ8=
			</data>
			<key>hash2</key>
			<data>
			zGnJLLtwTXk2sK6kOm5gTYsNOSTF8RFGN+QVgksRQw4=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			uB809vHmg0WJKozHcK4Lk6PUt2g=
			</data>
			<key>hash2</key>
			<data>
			5agfeYY/jHf5Yy9vmxuNXcAaNX4BdS7BaZAKUkLb9TY=
			</data>
		</dict>
		<key>Resources/InterruptTemplate.png</key>
		<dict>
			<key>hash</key>
			<data>
			U416kJFqeffEEnN+UHR87uV72F0=
			</data>
			<key>hash2</key>
			<data>
			92ev4FfmogSyeZrkcr5KbcinSPWiUZPeAWu6SYxvJqQ=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			EtWR1h4skW3fAPOxXIu4FLCtrXk=
			</data>
			<key>hash2</key>
			<data>
			jvEr9EtW90wM8mWlyLgkKZpphrlUdG1+1Q5rhw9lf5Q=
			</data>
		</dict>
		<key>Resources/JoyConDualTemplate.png</key>
		<dict>
			<key>hash</key>
			<data>
			aPFMamJhSECRQk1Rggj/A1VeKvo=
			</data>
			<key>hash2</key>
			<data>
			Stbd1eeYftDVWEgLgzmyXMg3afkRLZIAaJQMDjZzKO4=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			BmYqx0zglOKJdMEuOMqTRjGQm4Q=
			</data>
			<key>hash2</key>
			<data>
			JqGONSGIomigtMA2PUopEinYyNWtA0Amzr+zfLyEQec=
			</data>
		</dict>
		<key>Resources/JoyConLeftTemplate.png</key>
		<dict>
			<key>hash</key>
			<data>
			QhNVLLWCGIZ/d+izTgfcP7yF4RI=
			</data>
			<key>hash2</key>
			<data>
			EdaszAwAnN4tnfEUh6gFh98BB5IbXRQM1h8tYRoVMNQ=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			0hp2cqr5dH5JraENaA0AsI2gjHA=
			</data>
			<key>hash2</key>
			<data>
			oFivPPlGlsVd3NnsyhAO9J+aq9S28g+PG/9sSBOb6WA=
			</data>
		</dict>
		<key>Resources/JoyConRightTemplate.png</key>
		<dict>
			<key>hash</key>
			<data>
			1SKjjYro/37WoZT8ugv85ZNphuc=
			</data>
			<key>hash2</key>
			<data>
			IhMOTG3SeR0e7G1jutY9TJws5NFB5bO1fhBPQ0L9bFk=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			MnA7mpfVg4+z8N7DLtY1TNoF0xk=
			</data>
			<key>hash2</key>
			<data>
			NjJn5Ncf4Fqk014rr5Fy4OfTFcv6kI/+mpmoh6hBqWY=
			</data>
		</dict>
		<key>Resources/Joypad.png</key>
		<dict>
			<key>hash</key>
			<data>
			QiaxEKNGrneM98wTiEACDEwNcyc=
			</data>
			<key>hash2</key>
			<data>
			SZjAKmWGBwRjPWIfjGPYjKGiIw1dNhDV5cKusnMOXBY=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			H7bgoff5bxuIMk5xnSRoRhyBfDc=
			</data>
			<key>hash2</key>
			<data>
			nGwAiSaRHH4S3KXID3DHD+PMVT313nZJ2D5hsJOCfxo=
			</data>
		</dict>
		<key>Resources/Joypad~dark.png</key>
		<dict>
			<key>hash</key>
			<data>
			wGy4sxvaQyEQ+/tBCP2TH3weVsI=
			</data>
			<key>hash2</key>
			<data>
			8Rz+M8GQS31UbfeMrILq6fugct4wLAoB0R+9FCIx+kw=
			</data>
		</dict>
		<key>Resources/Joypad~<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			x0DYCuQgxZrWQhW1gRmJlSV/U3w=
			</data>
			<key>hash2</key>
			<data>
			k2ZMF5zU8cREi1bzIvbTCDswZzq1tPUQRa95N7GOfRM=
			</data>
		</dict>
		<key>Resources/Joypad~solid.png</key>
		<dict>
			<key>hash</key>
			<data>
			KwPpRrrHWkhvnTonMe0jqfoQnMk=
			</data>
			<key>hash2</key>
			<data>
			mi9wo2e68VtqT6/0xJ0INm44rD9gm8xnLM9S3mkLonk=
			</data>
		</dict>
		<key>Resources/Joypad~<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			h6+UnewKt+h+CNz74HNo6Zif9is=
			</data>
			<key>hash2</key>
			<data>
			k7vdbfgQ1ExkTZvEmoLYX11FNuuNdPWyxMRp1UM+/Ng=
			</data>
		</dict>
		<key>Resources/MainMenu.nib/keyedobjects-101300.nib</key>
		<dict>
			<key>hash</key>
			<data>
			lvFK5XiQswbEF+zZTbAwE0ojEo0=
			</data>
			<key>hash2</key>
			<data>
			M/ysEoFjc+8GeIl3aiQlrP202TjYdsmKvrnEF8LaKxA=
			</data>
		</dict>
		<key>Resources/MainMenu.nib/keyedobjects.nib</key>
		<dict>
			<key>hash</key>
			<data>
			6Hg/kbqRR4S4XsSWrEivW3OqLpE=
			</data>
			<key>hash2</key>
			<data>
			PnO2veG8nXRNKGs/iIbmD58Ht2JEde94kiEO6g3To8g=
			</data>
		</dict>
		<key>Resources/Next.png</key>
		<dict>
			<key>hash</key>
			<data>
			dW/Nuxb3BcG64ZE+FNOL6ueg8gA=
			</data>
			<key>hash2</key>
			<data>
			4fMCKum0F9fMEO6yS/ABzSgw8ArqW47KYkW0VWjMvo0=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			Np4OouHxRPtU9N5xLrWU4gabhVs=
			</data>
			<key>hash2</key>
			<data>
			khxrxiwCpI3UpLictOYek1X/HTIWCa9XpT3Gzmphxts=
			</data>
		</dict>
		<key>Resources/NextTemplate.png</key>
		<dict>
			<key>hash</key>
			<data>
			SuksKCSwxrOo/8pqN2xzGbLx6/o=
			</data>
			<key>hash2</key>
			<data>
			6upQZg7yt3/UwJ0xkcX9vu3fi2QFE3vr8wE/AXH5e4Y=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			YfgIwuaKQ7kAzHTEZC3Apzxpsrk=
			</data>
			<key>hash2</key>
			<data>
			XVo46X4BKc5GyRid71mjxK+GUXwDZhHXmiX2pwaY/8g=
			</data>
		</dict>
		<key>Resources/Pause.png</key>
		<dict>
			<key>hash</key>
			<data>
			8hcOOEVixHrdZ63BFi8PJHoQUBY=
			</data>
			<key>hash2</key>
			<data>
			gfB0631tY9SmWetU0FwqkkWinR7joRcZSA7Q9qyhZyc=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			7PPS3ek4OlkJl9nq0zwTsP/U1kY=
			</data>
			<key>hash2</key>
			<data>
			4RHEn9ehVDvgzi+V/WxTzgI5KjlC0t6qdzyMxsHBMfY=
			</data>
		</dict>
		<key>Resources/Play.png</key>
		<dict>
			<key>hash</key>
			<data>
			JzEAOCLSBtSyKjtyU9r9f7tv8c4=
			</data>
			<key>hash2</key>
			<data>
			z2AQt8tRV5KR+rTBdg43KRcw0/ju0rtL42bt5DMk8g0=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			Xzqf/n0SSKcObhRk/1o9m5rKEek=
			</data>
			<key>hash2</key>
			<data>
			eEglkSxBmgv3xUbJeIOgFynfWGFyNdSd/ILaSvlF/0A=
			</data>
		</dict>
		<key>Resources/PopoverView.nib/keyedobjects-101300.nib</key>
		<dict>
			<key>hash</key>
			<data>
			tZ8p0PxF/ptR14tdr6wSIo9UNj0=
			</data>
			<key>hash2</key>
			<data>
			wtHkBbjeGql9QBPitqN17X2TZkRO2HjfM1/yqugZTy4=
			</data>
		</dict>
		<key>Resources/PopoverView.nib/keyedobjects.nib</key>
		<dict>
			<key>hash</key>
			<data>
			LlWwNSGK+MZrnAgqBmYlfmVJ9Gk=
			</data>
			<key>hash2</key>
			<data>
			J85yCLw5SpnB6aXJtxVNi+SgwYoiZtx/HfGrZJxDltg=
			</data>
		</dict>
		<key>Resources/Preferences.nib/keyedobjects-101300.nib</key>
		<dict>
			<key>hash</key>
			<data>
			6yMFQtkgrnO17r7SNRvheXUqjhE=
			</data>
			<key>hash2</key>
			<data>
			N7V3vRl3GKfBz2ZGWh0sRcze2R9fOuB1gkDehO01yC0=
			</data>
		</dict>
		<key>Resources/Preferences.nib/keyedobjects-110000.nib</key>
		<dict>
			<key>hash</key>
			<data>
			ccyKkFnYqiH0g1U7a+yqurFLzq0=
			</data>
			<key>hash2</key>
			<data>
			Wi5xlpwYxG77VvpyHmNUde14dRoYilgGpSrmLwEXOXw=
			</data>
		</dict>
		<key>Resources/Preferences.nib/keyedobjects.nib</key>
		<dict>
			<key>hash</key>
			<data>
			BJK4LmqCs76MG8+f8vYLNJiEG3E=
			</data>
			<key>hash2</key>
			<data>
			0e+kDOeHAH3La8FRMaev5GxW/C1ltpmdGMNNohmF5I0=
			</data>
		</dict>
		<key>Resources/Previous.png</key>
		<dict>
			<key>hash</key>
			<data>
			w3SFZP5T1FSm46FcxbYzl3xAKx8=
			</data>
			<key>hash2</key>
			<data>
			AO5f2/nDzmNwcZuIYbDa209y6Gg3DvxCQklIUzNYntA=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			ORTopSE+Py/5AUdAVaEW5LuR+nk=
			</data>
			<key>hash2</key>
			<data>
			ERl4dTNlfg+2GmQ/mMbTfOEuSXYU+WBHSJFLNJ9bOgM=
			</data>
		</dict>
		<key>Resources/Rewind.png</key>
		<dict>
			<key>hash</key>
			<data>
			YnDGAepfKpgmWrgihg5Y2PxUOjc=
			</data>
			<key>hash2</key>
			<data>
			ifq+o/0QcyVe0KiSZ7ayUFOlvhyE58Ob7GtTSAdvwVg=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			V99AciTISZjDiqkbAjakPJC7YFw=
			</data>
			<key>hash2</key>
			<data>
			6NYZmjykdzOgqO8JKZapusUP3sk+X4fTttvK3mMRKns=
			</data>
		</dict>
		<key>Resources/Shaders/AAOmniScaleLegacy.fsh</key>
		<dict>
			<key>hash</key>
			<data>
			3wXKCc5VFd8Nl5uDgChdiEk2lb4=
			</data>
			<key>hash2</key>
			<data>
			7nk4tpjNM0IYuZ7nAwB6LsSKPA7vi0DtE0Nb3J503hk=
			</data>
		</dict>
		<key>Resources/Shaders/AAScale2x.fsh</key>
		<dict>
			<key>hash</key>
			<data>
			8ZUYkTfhZVij7sq6ei39RV8ib6Q=
			</data>
			<key>hash2</key>
			<data>
			9pwAQQW1zupvF+Q9ESIUIZNzinnoOiYCcl9aDwbY5Zw=
			</data>
		</dict>
		<key>Resources/Shaders/AAScale4x.fsh</key>
		<dict>
			<key>hash</key>
			<data>
			2JKgPCvqwwG4cKtof+UwNxrHTZo=
			</data>
			<key>hash2</key>
			<data>
			8tE0Pi1veKhxZ1CjqQikEcn/5Wb9LWMoeAGJr1NWINA=
			</data>
		</dict>
		<key>Resources/Shaders/Bilinear.fsh</key>
		<dict>
			<key>hash</key>
			<data>
			rm3VBA/I4xGPJzIKQESWW/XIE9I=
			</data>
			<key>hash2</key>
			<data>
			HYjOpp/5d052KW364bjglAMK9oR83SrAVkYtIctnWT0=
			</data>
		</dict>
		<key>Resources/Shaders/CRT.fsh</key>
		<dict>
			<key>hash</key>
			<data>
			N0xzNk8kyb6yEilzTwozLSZ9O1k=
			</data>
			<key>hash2</key>
			<data>
			9CdYC2jXiH76LtiwpO5eV0jTz6/FpanK/ETonv1gg2s=
			</data>
		</dict>
		<key>Resources/Shaders/FlatCRT.fsh</key>
		<dict>
			<key>hash</key>
			<data>
			LJ2BWFeIT98jo1O6vtX2FrzNR1o=
			</data>
			<key>hash2</key>
			<data>
			rQbGqKs43fDNMQxNYJJRUbzIjmztsmds0KyKfs1gFTU=
			</data>
		</dict>
		<key>Resources/Shaders/HQ2x.fsh</key>
		<dict>
			<key>hash</key>
			<data>
			dhQGj7kbG4A2/n+UoG9nQ2TguR4=
			</data>
			<key>hash2</key>
			<data>
			0v8V2YCquC4IWo4IwP1bd01L0nxjCZz+xZ31IGYMGX4=
			</data>
		</dict>
		<key>Resources/Shaders/LCD.fsh</key>
		<dict>
			<key>hash</key>
			<data>
			LJKNG9JVWsaaDD3Ysa5ELwYHUng=
			</data>
			<key>hash2</key>
			<data>
			ie1piGV2oywoqR3qZP4WT8Bemvoz/lwlYu3eD9RR/aA=
			</data>
		</dict>
		<key>Resources/Shaders/MasterShader.fsh</key>
		<dict>
			<key>hash</key>
			<data>
			nLUUqGvJz22njMkw0Zl2Z4aJJ8A=
			</data>
			<key>hash2</key>
			<data>
			+cITxHqci+FKQVlKbKXRQu/uap83ZvuzmPtzijIZDdg=
			</data>
		</dict>
		<key>Resources/Shaders/MasterShader.metal</key>
		<dict>
			<key>hash</key>
			<data>
			T/28d6orNIuR1FGAI7VUnRZQqVM=
			</data>
			<key>hash2</key>
			<data>
			j/iKqZapxL9baqMOA9sNRoWBxvSWsu3X5vkzTadsaNU=
			</data>
		</dict>
		<key>Resources/Shaders/MonoLCD.fsh</key>
		<dict>
			<key>hash</key>
			<data>
			aLIJP6SxX3iY2OYLu+K01pNLAko=
			</data>
			<key>hash2</key>
			<data>
			w+k/DKF921SvdRhNxBy53WojoxRO7Kff+58EYTq4qrw=
			</data>
		</dict>
		<key>Resources/Shaders/NearestNeighbor.fsh</key>
		<dict>
			<key>hash</key>
			<data>
			u+KfKwz6i5NWFtTql0EsZUjoHU4=
			</data>
			<key>hash2</key>
			<data>
			oETcuwdhMrGzLHN+2qcz6iYExIGf2OET7xJlj1bQQhE=
			</data>
		</dict>
		<key>Resources/Shaders/OmniScale.fsh</key>
		<dict>
			<key>hash</key>
			<data>
			GJoOSXJrm00rPOzmcz9wiPrHlJ0=
			</data>
			<key>hash2</key>
			<data>
			ezuET/hW7dhqPTfFlm/6OMtF6H1wyTKD/rw4UvAeymc=
			</data>
		</dict>
		<key>Resources/Shaders/OmniScaleLegacy.fsh</key>
		<dict>
			<key>hash</key>
			<data>
			nmly441l/ivqXu7+tYQkhrDRbYg=
			</data>
			<key>hash2</key>
			<data>
			R/lqUO41n9b2bczgFUJRJ0uSBmsA1zJHFCcxWCUL5Sk=
			</data>
		</dict>
		<key>Resources/Shaders/Scale2x.fsh</key>
		<dict>
			<key>hash</key>
			<data>
			Y4iop9rQCc79m8fX48BS1Tt+YZU=
			</data>
			<key>hash2</key>
			<data>
			VNO5MY6Iw1UNCdoDkfnYVl+ihfbTPHn8O8KxUT7PnoE=
			</data>
		</dict>
		<key>Resources/Shaders/Scale4x.fsh</key>
		<dict>
			<key>hash</key>
			<data>
			JTj1y9WMMdb1eF8tXGcUnIfVuaw=
			</data>
			<key>hash2</key>
			<data>
			UtiurDgLf/9eNMHkpXXe8eNJCd6/7XN32Hjgz+tDe7Y=
			</data>
		</dict>
		<key>Resources/Shaders/SmoothBilinear.fsh</key>
		<dict>
			<key>hash</key>
			<data>
			KVBM1SDpVH4FGt+hczgkH7JY5OU=
			</data>
			<key>hash2</key>
			<data>
			hCOms4J3FNh2cHxD8Y7VlzH8TFQTYvurVHHDkAzpuN0=
			</data>
		</dict>
		<key>Resources/Speaker.png</key>
		<dict>
			<key>hash</key>
			<data>
			eqy6nJ31oK86gm0ilOWtN6ToefU=
			</data>
			<key>hash2</key>
			<data>
			UAUU3SJ9iGEN6+6tVKfwLkn8dI38/21gbDT2dlip/DU=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			EYpo+FkCcXHJC4cNlCsBWVU098Y=
			</data>
			<key>hash2</key>
			<data>
			e79wvrmNklVRiIx+y+51kIj6euWs6dxj+Svz/AG2hQQ=
			</data>
		</dict>
		<key>Resources/Speaker~dark.png</key>
		<dict>
			<key>hash</key>
			<data>
			lOo44KAyCnp44cL6tL/LE5tjIbo=
			</data>
			<key>hash2</key>
			<data>
			aw/t1eNV3pXMn2PTqkctNOIEamLbrvDQrcCtssLCiZw=
			</data>
		</dict>
		<key>Resources/Speaker~<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			isy7doI7LBUA88TQefUQoZMnT0I=
			</data>
			<key>hash2</key>
			<data>
			LFAUFd+C8NGA9/DVkJp09l6Ua3PT+7v5x4q8grXCykE=
			</data>
		</dict>
		<key>Resources/Speaker~solid.png</key>
		<dict>
			<key>hash</key>
			<data>
			0E53fybX93ODjXVnHf1Cwdxyed8=
			</data>
			<key>hash2</key>
			<data>
			yrdTeWgVjuSzHbHC5SQoxLeXtBfwv4Lbn0gQakk+v9A=
			</data>
		</dict>
		<key>Resources/Speaker~<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			/aSAtsQ8trsxaotU2gMDj2TWnHU=
			</data>
			<key>hash2</key>
			<data>
			Zw/yxLYeIhp0JuJ9isFUSjQrZKLWnSjhD4r+YOHhUt4=
			</data>
		</dict>
		<key>Resources/StepTemplate.png</key>
		<dict>
			<key>hash</key>
			<data>
			fis/htCuJtN40Fx8NEhd+fqCa/0=
			</data>
			<key>hash2</key>
			<data>
			9KeRF59q/FbQQ35I14WAmlMyDVG69wvzqCNfsc8Ubh4=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			9CcW5+zJuwtEAQ3N3eCHjklw3Vs=
			</data>
			<key>hash2</key>
			<data>
			M3lZwwkrYxD8Ywka0AbgFAJiRFkxSZLj60Txp7H64Yc=
			</data>
		</dict>
		<key>Resources/UpdateWindow.nib/keyedobjects-101300.nib</key>
		<dict>
			<key>hash</key>
			<data>
			mWegjJ2IUPEeC+S3QKirsxpnWz8=
			</data>
			<key>hash2</key>
			<data>
			Yt8QFcq/ABJc2d07qPAtm7zGqP1jtXl/odO8//ptSy4=
			</data>
		</dict>
		<key>Resources/UpdateWindow.nib/keyedobjects.nib</key>
		<dict>
			<key>hash</key>
			<data>
			ToCsFQFKc31E39GITi+AxgmcJUY=
			</data>
			<key>hash2</key>
			<data>
			9wvGVKAID12/Nncsapwsf5jaeO0R/sVbNChKBX+DuTg=
			</data>
		</dict>
		<key>Resources/Updates.png</key>
		<dict>
			<key>hash</key>
			<data>
			WKdJcKdI+Lw9OgTl2gjXp292WSE=
			</data>
			<key>hash2</key>
			<data>
			hF2FUmsOOBDPj+/MncWT9T0dpkb7LmSCs/D8sKLUYq0=
			</data>
		</dict>
		<key>Resources/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			PaXS8p5Z+jSWVk/yb1rNDdZMqJ8=
			</data>
			<key>hash2</key>
			<data>
			zGnJLLtwTXk2sK6kOm5gTYsNOSTF8RFGN+QVgksRQw4=
			</data>
		</dict>
		<key>Resources/Updates~solid.png</key>
		<dict>
			<key>hash</key>
			<data>
			ut525kIdWlKumYWm64JZp/7N6J4=
			</data>
			<key>hash2</key>
			<data>
			EkTa3t/DlWebS0gUChGQ0DF4Ggv7+DqOjzDevAkovH4=
			</data>
		</dict>
		<key>Resources/Updates~<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			YGtQYKaHHWk8RxQEIG4gASInvY0=
			</data>
			<key>hash2</key>
			<data>
			+QLcWx4MPSpKu1Ut5XMcDGOfXYNY1ZKdAjY5gq/H4RE=
			</data>
		</dict>
		<key>Resources/Updates~solid~dark.png</key>
		<dict>
			<key>hash</key>
			<data>
			DGqfgvFLelZV3HnhgyQqMgMf6iM=
			</data>
			<key>hash2</key>
			<data>
			2XzZnUjSFkzTN8X9bE67DyZKXsMESRDAEeX9a+F5psc=
			</data>
		</dict>
		<key>Resources/Updates~solid~<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			PnClHyWPa5TkJIoJx2qWd11II/c=
			</data>
			<key>hash2</key>
			<data>
			s4WzcsEXqcnZPJI0I/wfvrP4eJabU754Ut7DyVNVHCs=
			</data>
		</dict>
		<key>Resources/agb_boot.bin</key>
		<dict>
			<key>hash</key>
			<data>
			TtHZXJxoKnhpjyNc6em7O2K8z58=
			</data>
			<key>hash2</key>
			<data>
			ZI/SreNad86T+0zqx1Sw8EZfHq8NsMvvG+1Vv9inF5Q=
			</data>
		</dict>
		<key>Resources/cgb0_boot.bin</key>
		<dict>
			<key>hash</key>
			<data>
			gico++HWN1KMV2Wa2G4L9dpmZnc=
			</data>
			<key>hash2</key>
			<data>
			LCl7bLdizQpQJTRJ/QJq4wx28MwwuRni/0mLynaC6sw=
			</data>
		</dict>
		<key>Resources/cgb_boot.bin</key>
		<dict>
			<key>hash</key>
			<data>
			ETkDd1qdNLeYwvgHZnLaZiaBWpE=
			</data>
			<key>hash2</key>
			<data>
			92e45+UQolX4EyjInbpuDJlrNw4byGrruFhKfaR6W7o=
			</data>
		</dict>
		<key>Resources/dmg_boot.bin</key>
		<dict>
			<key>hash</key>
			<data>
			HbV6HotuQJb4EVh/nqsMZnX9l1U=
			</data>
			<key>hash2</key>
			<data>
			b2TaTOzX5U4vko6z47p4EKelZ9DSR8xxc30XceBzqRY=
			</data>
		</dict>
		<key>Resources/mgb_boot.bin</key>
		<dict>
			<key>hash</key>
			<data>
			mBe9rpM1o6zLWE93wWXcVyZVXR8=
			</data>
			<key>hash2</key>
			<data>
			EutLlvjCwZzVHCCa8fClqhngH4hhFK6EglilkvQI704=
			</data>
		</dict>
		<key>Resources/registers.sym</key>
		<dict>
			<key>hash</key>
			<data>
			ud/QDbrHIhKPjlp7ROVCnIrP+do=
			</data>
			<key>hash2</key>
			<data>
			a3ykk5EsDJVgZNYmvM6yTNlrWM2yPCRuH9X3LAlrLYM=
			</data>
		</dict>
		<key>Resources/sgb2_boot.bin</key>
		<dict>
			<key>hash</key>
			<data>
			8oKzqvmPhCPat9d/GqAZK+Yw8vs=
			</data>
			<key>hash2</key>
			<data>
			imVGWp2n7GV3JqZx2pqFljzxnSyXXkma6muX6zfgtuo=
			</data>
		</dict>
		<key>Resources/sgb_boot.bin</key>
		<dict>
			<key>hash</key>
			<data>
			Np5uteDJdeqlKko/buB7Kjw8Ft4=
			</data>
			<key>hash2</key>
			<data>
			tg1JOnlEzPdMgfHntr84wscCnilmSOoOdMsisQ3R/Lg=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
