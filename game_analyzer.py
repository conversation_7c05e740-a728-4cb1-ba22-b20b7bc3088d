#!/usr/bin/env python3
"""
游戏状态分析工具
帮助调试和设置正确的游戏区域和检测逻辑
"""

import cv2
import numpy as np
import pyautogui
import subprocess
import tempfile
import os
import time

class GameAnalyzer:
    def __init__(self):
        self.game_region = (15, 105, 520, 520)  # 当前游戏区域设置
        
    def capture_screen(self):
        """截取屏幕"""
        try:
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
                tmp_path = tmp_file.name
            
            # 使用macOS的screencapture命令
            result = subprocess.run(['screencapture', '-x', tmp_path], 
                                  capture_output=True, text=True, timeout=5)
            
            if result.returncode != 0:
                print(f"截屏失败: {result.stderr}")
                return None
                
            # 读取截图
            frame = cv2.imread(tmp_path)
            os.unlink(tmp_path)  # 删除临时文件
            
            if frame is None:
                print("无法读取截图文件")
                return None
                
            return frame
            
        except Exception as e:
            print(f"截屏出错: {e}")
            return None
    
    def analyze_full_screen(self):
        """分析完整屏幕，帮助找到正确的游戏区域"""
        print("=== 完整屏幕分析 ===")
        
        frame = self.capture_screen()
        if frame is None:
            return
            
        # 保存完整截图
        cv2.imwrite('full_screen.png', frame)
        print(f"完整截图已保存: full_screen.png")
        print(f"屏幕尺寸: {frame.shape[1]} x {frame.shape[0]}")
        
        # 在当前游戏区域画框
        x, y, w, h = self.game_region
        marked_frame = frame.copy()
        cv2.rectangle(marked_frame, (x, y), (x + w, y + h), (0, 255, 0), 3)
        cv2.putText(marked_frame, f"Current Game Region: {self.game_region}", 
                   (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        cv2.imwrite('screen_with_region.png', marked_frame)
        print("标记游戏区域的截图已保存: screen_with_region.png")
        
        # 保存当前游戏区域的裁剪图
        if x >= 0 and y >= 0 and x + w <= frame.shape[1] and y + h <= frame.shape[0]:
            game_area = frame[y:y+h, x:x+w]
            cv2.imwrite('current_game_area.png', game_area)
            print("当前游戏区域裁剪图已保存: current_game_area.png")
        else:
            print("警告: 游戏区域坐标超出屏幕范围!")
    
    def analyze_game_area(self):
        """详细分析游戏区域"""
        print("=== 游戏区域详细分析 ===")
        
        frame = self.capture_screen()
        if frame is None:
            return
            
        x, y, w, h = self.game_region
        
        # 检查坐标是否有效
        if x < 0 or y < 0 or x + w > frame.shape[1] or y + h > frame.shape[0]:
            print(f"错误: 游戏区域 {self.game_region} 超出屏幕范围 {frame.shape[1]}x{frame.shape[0]}")
            return
            
        game_area = frame[y:y+h, x:x+w]
        
        # 保存游戏区域图片
        cv2.imwrite('game_area_analysis.png', game_area)
        print(f"游戏区域图片已保存: game_area_analysis.png")
        print(f"游戏区域尺寸: {game_area.shape[1]} x {game_area.shape[0]}")
        
        # 颜色分析
        self._analyze_colors(game_area)
        
        # 检测不同的游戏状态
        print("\n--- 游戏状态检测 ---")
        
        # 检测是否是开始界面
        start_detected = self._detect_start_screen(game_area)
        print(f"开始界面检测: {start_detected}")
        
        # 检测是否是游戏进行中
        playing_detected = self._detect_playing_screen(game_area)
        print(f"游戏进行中检测: {playing_detected}")
        
        # 检测是否是菜单界面
        menu_detected = self._detect_menu_screen(game_area)
        print(f"菜单界面检测: {menu_detected}")
        
    def _analyze_colors(self, frame):
        """分析图像中的颜色分布"""
        print("\n--- 颜色分析 ---")
        
        # 转换为HSV
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        
        # 定义颜色范围
        color_ranges = {
            '黑色': ([0, 0, 0], [180, 255, 50]),
            '白色': ([0, 0, 200], [180, 50, 255]),
            '蓝色': ([100, 50, 50], [130, 255, 255]),
            '绿色': ([40, 50, 50], [80, 255, 255]),
            '红色': ([0, 50, 50], [10, 255, 255]),
            '黄色': ([20, 50, 50], [30, 255, 255]),
        }
        
        total_pixels = frame.shape[0] * frame.shape[1]
        
        for color_name, (lower, upper) in color_ranges.items():
            lower = np.array(lower)
            upper = np.array(upper)
            mask = cv2.inRange(hsv, lower, upper)
            ratio = np.sum(mask > 0) / total_pixels
            print(f"{color_name}: {ratio:.3f} ({ratio*100:.1f}%)")
            
            # 保存颜色掩码用于调试
            cv2.imwrite(f'color_mask_{color_name}.png', mask)
    
    def _detect_start_screen(self, frame):
        """检测开始界面"""
        try:
            # 转换为灰度
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # 检测高亮文字（开始界面通常有亮色文字）
            bright_mask = cv2.threshold(gray, 180, 255, cv2.THRESH_BINARY)[1]
            bright_ratio = np.sum(bright_mask > 0) / (frame.shape[0] * frame.shape[1])
            
            # 检测中等亮度区域（背景）
            mid_mask = cv2.threshold(gray, 100, 255, cv2.THRESH_BINARY)[1]
            mid_ratio = np.sum(mid_mask > 0) / (frame.shape[0] * frame.shape[1])
            
            print(f"  亮色文字比例: {bright_ratio:.3f}")
            print(f"  中等亮度比例: {mid_ratio:.3f}")
            
            # 开始界面特征：有一些亮色文字，但不是全亮
            return bright_ratio > 0.05 and bright_ratio < 0.5 and mid_ratio > 0.3
            
        except Exception as e:
            print(f"开始界面检测出错: {e}")
            return False
    
    def _detect_playing_screen(self, frame):
        """检测游戏进行中界面"""
        try:
            # 转换为灰度
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # 检测边缘（游戏中有很多方块边缘）
            edges = cv2.Canny(gray, 30, 100)
            edge_ratio = np.sum(edges > 0) / (frame.shape[0] * frame.shape[1])
            
            # 检测深色区域（游戏板背景）
            dark_mask = cv2.threshold(gray, 80, 255, cv2.THRESH_BINARY_INV)[1]
            dark_ratio = np.sum(dark_mask > 0) / (frame.shape[0] * frame.shape[1])
            
            print(f"  边缘比例: {edge_ratio:.3f}")
            print(f"  深色背景比例: {dark_ratio:.3f}")
            
            # 游戏进行中特征：有边缘和深色背景
            return edge_ratio > 0.01 and dark_ratio > 0.3
            
        except Exception as e:
            print(f"游戏进行检测出错: {e}")
            return False
    
    def _detect_menu_screen(self, frame):
        """检测菜单界面"""
        try:
            # 转换为灰度
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # 检测文字轮廓
            edges = cv2.Canny(gray, 50, 150)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 统计可能是文字的轮廓
            text_contours = 0
            for contour in contours:
                area = cv2.contourArea(contour)
                if 50 < area < 2000:  # 文字大小范围
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = w / h if h > 0 else 0
                    if 0.2 < aspect_ratio < 5:  # 文字的宽高比
                        text_contours += 1
            
            print(f"  文字轮廓数量: {text_contours}")
            
            # 菜单界面特征：有多个文字轮廓
            return text_contours > 8
            
        except Exception as e:
            print(f"菜单界面检测出错: {e}")
            return False
    
    def test_buttons(self):
        """测试按键功能"""
        print("=== 按键测试 ===")
        print("请确保游戏窗口处于活动状态")
        print("将在3秒后开始测试...")
        
        for i in range(3, 0, -1):
            print(f"{i}...")
            time.sleep(1)
        
        buttons = ['x', 'z', 'left', 'right', 'down']
        for button in buttons:
            print(f"测试按键: {button}")
            pyautogui.press(button)
            time.sleep(0.8)
        
        print("按键测试完成")
    
    def adjust_region(self):
        """调整游戏区域"""
        print("=== 调整游戏区域 ===")
        print(f"当前游戏区域: {self.game_region}")
        print("请输入新的游戏区域坐标")
        
        try:
            x = int(input("X坐标: "))
            y = int(input("Y坐标: "))
            w = int(input("宽度: "))
            h = int(input("高度: "))
            
            self.game_region = (x, y, w, h)
            print(f"游戏区域已更新为: {self.game_region}")
            
        except ValueError:
            print("输入无效，保持原设置")

def main():
    analyzer = GameAnalyzer()
    
    while True:
        print("\n=== 游戏状态分析工具 ===")
        print("1. 分析完整屏幕")
        print("2. 分析游戏区域")
        print("3. 测试按键")
        print("4. 调整游戏区域")
        print("5. 退出")
        
        choice = input("请选择 (1-5): ").strip()
        
        if choice == '1':
            analyzer.analyze_full_screen()
        elif choice == '2':
            analyzer.analyze_game_area()
        elif choice == '3':
            analyzer.test_buttons()
        elif choice == '4':
            analyzer.adjust_region()
        elif choice == '5':
            break
        else:
            print("无效选择")

if __name__ == "__main__":
    main()
