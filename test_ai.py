#!/usr/bin/env python3
"""
Tetris AI 测试脚本
用于测试AI的各个组件功能
"""

import numpy as np
import time
from tetris_ai import TetrisAI

def test_board_evaluation():
    """测试评估函数"""
    print("测试评估函数...")
    
    ai = TetrisAI(debug=False)
    
    # 创建测试棋盘
    test_boards = [
        # 空棋盘
        np.zeros((20, 10), dtype=int),
        
        # 底部有一些方块
        np.array([[0]*10 for _ in range(17)] + [[1]*10, [1]*9+[0], [1]*8+[0]*2]),
        
        # 有洞穴的棋盘
        np.array([[0]*10 for _ in range(17)] + [[1]*10, [1,0,1,1,1,1,1,1,1,1], [1]*10])
    ]
    
    for i, board in enumerate(test_boards):
        score = ai.evaluate_board(board)
        print(f"测试棋盘 {i+1} 评分: {score}")
        ai.visualize_board(board, f"测试棋盘 {i+1}")
    
    print("评估函数测试完成\n")

def test_piece_rotation():
    """测试方块旋转"""
    print("测试方块旋转...")
    
    ai = TetrisAI(debug=False)
    
    for piece_name, piece in ai.pieces.items():
        print(f"\n{piece_name} 方块旋转测试:")
        current = piece
        for rotation in range(4):
            print(f"旋转 {rotation}:")
            for row in current:
                print("  " + "".join("█" if cell else "·" for cell in row))
            current = ai.rotate_piece(current)
    
    print("方块旋转测试完成\n")

def test_collision_detection():
    """测试碰撞检测"""
    print("测试碰撞检测...")
    
    ai = TetrisAI(debug=False)
    
    # 创建测试棋盘
    board = np.zeros((20, 10), dtype=int)
    board[19] = [1, 1, 1, 0, 0, 0, 1, 1, 1, 1]  # 底部有一些方块
    
    # 测试I方块
    i_piece = ai.pieces['I']
    
    test_positions = [
        (3, 18),  # 应该碰撞
        (3, 17),  # 应该不碰撞
        (0, 19),  # 边界碰撞
        (7, 19),  # 应该碰撞
    ]
    
    for x, y in test_positions:
        collision = ai.check_collision(board, i_piece, x, y)
        print(f"位置 ({x}, {y}): {'碰撞' if collision else '无碰撞'}")
    
    print("碰撞检测测试完成\n")

def test_move_simulation():
    """测试移动模拟"""
    print("测试移动模拟...")
    
    ai = TetrisAI(debug=False)
    
    # 创建测试棋盘
    board = np.zeros((20, 10), dtype=int)
    board[19] = [1, 1, 0, 0, 0, 0, 0, 0, 1, 1]
    
    print("原始棋盘:")
    ai.visualize_board(board)
    
    # 模拟放置I方块
    simulated_board, lines_cleared = ai.simulate_move(board, 'I', 2, 0, 0)
    
    print("放置I方块后:")
    ai.visualize_board(simulated_board)
    print(f"消除行数: {lines_cleared}")
    
    print("移动模拟测试完成\n")

def test_performance():
    """性能测试"""
    print("性能测试...")
    
    ai = TetrisAI(debug=False)
    
    # 创建复杂棋盘
    board = np.random.randint(0, 2, (20, 10))
    
    # 测试评估函数性能
    start_time = time.time()
    for _ in range(1000):
        ai.evaluate_board(board)
    eval_time = time.time() - start_time
    
    print(f"1000次评估函数调用耗时: {eval_time:.3f}秒")
    print(f"平均每次: {eval_time/1000*1000:.3f}毫秒")
    
    # 测试决策性能
    ai.current_piece = 'T'
    ai.current_x = 4
    ai.current_y = 0
    
    start_time = time.time()
    for _ in range(100):
        ai.get_best_move(board)
    decision_time = time.time() - start_time
    
    print(f"100次决策调用耗时: {decision_time:.3f}秒")
    print(f"平均每次: {decision_time/100*1000:.3f}毫秒")
    
    print("性能测试完成\n")

def main():
    """主测试函数"""
    print("=== Tetris AI 组件测试 ===\n")
    
    try:
        test_board_evaluation()
        test_piece_rotation()
        test_collision_detection()
        test_move_simulation()
        test_performance()
        
        print("=== 所有测试完成 ===")
        print("如果没有错误信息，说明AI组件工作正常")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
