#!/usr/bin/env python3
"""
Tetris AI 调试版本
用于调试游戏检测和截图功能
"""

import cv2
import numpy as np
import time
import pyautogui
from tetris_ai import TetrisAI

class DebugTetrisAI(TetrisAI):
    def __init__(self):
        super().__init__(debug=True)
        self.screenshot_count = 0
    
    def debug_capture_and_save(self):
        """捕获屏幕并保存调试图像"""
        try:
            frame = self.capture_screen()
            if frame is not None:
                self.screenshot_count += 1
                filename = f"debug_screenshot_{self.screenshot_count}.png"
                cv2.imwrite(filename, frame)
                print(f"保存截图: {filename}, 尺寸: {frame.shape}")
                
                # 显示游戏区域信息
                print(f"游戏区域设置: {self.game_region}")
                
                # 测试各种检测
                game_start, pos = self.detect_game_start(frame)
                print(f"游戏开始检测: {game_start}, 位置: {pos}")
                
                game_over = self.detect_game_over(frame)
                print(f"游戏结束检测: {game_over}")
                
                piece, x, y = self.detect_current_piece(frame)
                print(f"当前方块检测: {piece} at ({x}, {y})")
                
                return frame
            else:
                print("截图失败")
                return None
        except Exception as e:
            print(f"调试截图出错: {e}")
            return None
    
    def interactive_debug(self):
        """交互式调试模式"""
        print("=== Tetris AI 交互式调试 ===")
        print("命令:")
        print("  s - 截图并分析")
        print("  r - 调整游戏区域")
        print("  t - 测试按键")
        print("  q - 退出")
        print("===============================")
        
        while True:
            cmd = input("\n输入命令: ").strip().lower()
            
            if cmd == 's':
                self.debug_capture_and_save()
            
            elif cmd == 'r':
                print(f"当前游戏区域: {self.game_region}")
                try:
                    x = int(input("输入X坐标: "))
                    y = int(input("输入Y坐标: "))
                    w = int(input("输入宽度: "))
                    h = int(input("输入高度: "))
                    self.game_region = (x, y, w, h)
                    print(f"游戏区域已更新为: {self.game_region}")
                except ValueError:
                    print("输入无效")
            
            elif cmd == 't':
                print("测试按键...")
                key = input("输入要测试的按键 (left/right/down/x/z): ")
                if key in ['left', 'right', 'down', 'x', 'z']:
                    pyautogui.press(key)
                    print(f"已按下 {key} 键")
                else:
                    print("无效按键")
            
            elif cmd == 'q':
                break
            
            else:
                print("未知命令")

def main():
    """主函数"""
    print("Tetris AI 调试工具")
    print("请确保SameBoy模拟器窗口可见")
    
    ai = DebugTetrisAI()
    
    # 首先截图看看当前状态
    print("\n正在截图分析当前状态...")
    ai.debug_capture_and_save()
    
    # 进入交互模式
    ai.interactive_debug()

if __name__ == "__main__":
    main()
