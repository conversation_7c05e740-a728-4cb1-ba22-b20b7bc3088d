#!/usr/bin/env python3
"""
调试截图工具 - 查看AI实际截取的游戏画面
"""

import cv2
import numpy as np
import subprocess
import tempfile
import os
from tetris_ai import TetrisAI

def capture_and_analyze():
    """截取并分析当前游戏画面"""
    # 创建AI实例来使用其截图功能
    ai = TetrisAI(debug=True)
    
    print("正在截取游戏画面...")
    frame = ai.capture_screen()
    
    if frame is None:
        print("❌ 截图失败")
        return
    
    print(f"✅ 截图成功，尺寸: {frame.shape}")
    
    # 保存完整截图
    cv2.imwrite("debug_full_screen.png", frame)
    print("📸 完整截图已保存为: debug_full_screen.png")
    
    # 提取游戏区域
    x, y, w, h = ai.game_region
    print(f"🎮 游戏区域坐标: x={x}, y={y}, w={w}, h={h}")
    
    if x + w > frame.shape[1] or y + h > frame.shape[0]:
        print(f"❌ 游戏区域超出截图范围！截图尺寸: {frame.shape[1]}x{frame.shape[0]}")
        return
    
    game_area = frame[y:y+h, x:x+w]
    cv2.imwrite("debug_game_area.png", game_area)
    print("🎯 游戏区域已保存为: debug_game_area.png")
    
    # 分析游戏区域
    print("\n🔍 游戏区域分析:")
    print(f"   尺寸: {game_area.shape}")
    
    # 颜色分析
    gray = cv2.cvtColor(game_area, cv2.COLOR_BGR2GRAY)
    
    # 统计不同亮度的像素
    black_pixels = np.sum(gray < 50)
    dark_pixels = np.sum((gray >= 50) & (gray < 100))
    medium_pixels = np.sum((gray >= 100) & (gray < 200))
    bright_pixels = np.sum(gray >= 200)
    total_pixels = game_area.shape[0] * game_area.shape[1]
    
    print(f"   黑色像素 (<50): {black_pixels} ({black_pixels/total_pixels*100:.1f}%)")
    print(f"   深色像素 (50-100): {dark_pixels} ({dark_pixels/total_pixels*100:.1f}%)")
    print(f"   中等像素 (100-200): {medium_pixels} ({medium_pixels/total_pixels*100:.1f}%)")
    print(f"   亮色像素 (>=200): {bright_pixels} ({bright_pixels/total_pixels*100:.1f}%)")
    
    # HSV分析
    hsv = cv2.cvtColor(game_area, cv2.COLOR_BGR2HSV)
    
    # 检测各种颜色
    colors = {
        "蓝色": ([100, 50, 50], [130, 255, 255]),
        "绿色": ([35, 50, 50], [85, 255, 255]),
        "红色": ([0, 50, 50], [20, 255, 255]),
        "黄色": ([20, 50, 50], [35, 255, 255]),
        "紫色": ([130, 50, 50], [160, 255, 255]),
        "青色": ([85, 50, 50], [100, 255, 255]),
    }
    
    print(f"\n🌈 颜色分析:")
    for color_name, (lower, upper) in colors.items():
        mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
        color_pixels = np.sum(mask > 0)
        print(f"   {color_name}: {color_pixels} ({color_pixels/total_pixels*100:.1f}%)")
    
    # 边缘检测
    edges = cv2.Canny(gray, 50, 150)
    edge_pixels = np.sum(edges > 0)
    print(f"\n📐 边缘检测:")
    print(f"   边缘像素: {edge_pixels} ({edge_pixels/total_pixels*100:.1f}%)")
    
    cv2.imwrite("debug_edges.png", edges)
    print("🔍 边缘检测结果已保存为: debug_edges.png")
    
    # 尝试检测方块
    print(f"\n🧩 方块检测测试:")
    board = ai.detect_blocks(frame)
    
    print("检测到的游戏板:")
    for i, row in enumerate(board):
        row_str = ""
        for cell in row:
            row_str += "█" if cell == 1 else "·"
        print(f"   {i:2d}: {row_str}")
    
    filled_cells = sum(sum(row) for row in board)
    print(f"   填充的格子数: {filled_cells}")

if __name__ == "__main__":
    capture_and_analyze()
