import pyautogui
import cv2
import numpy as np
import time
from PIL import ImageGrab

class TetrisAI:
    def __init__(self, debug=True):
        # 游戏窗口区域配置
        self.game_region = (0, 80, 660, 940)

        # 方块形状定义
        self.pieces = {
            'I': [[1, 1, 1, 1]],
            'O': [[1, 1], [1, 1]],
            'T': [[0, 1, 0], [1, 1, 1]],
            'L': [[0, 0, 1], [1, 1, 1]],
            'J': [[1, 0, 0], [1, 1, 1]],
            'S': [[0, 1, 1], [1, 1, 0]],
            'Z': [[1, 1, 0], [0, 1, 1]]
        }

        # 游戏状态
        self.game_started = False
        self.start_time = 0
        self.game_duration = 300  # 5分钟游戏时间

        # 当前方块信息
        self.current_piece = None
        self.current_x = 0
        self.current_y = 0
        self.current_rotation = 0

        # 动作控制
        self.last_action_time = 0
        self.action_delay = 0.15  # 优化动作延迟
        self.action_queue = []

        # 调试和统计
        self.debug = debug
        self.stats = {
            'pieces_placed': 0,
            'lines_cleared': 0,
            'total_score': 0,
            'game_time': 0,
            'actions_executed': 0,
            'detection_errors': 0
        }

        # 性能监控
        self.frame_times = []
        self.decision_times = []

        # 可视化
        self.show_debug_window = debug

        if self.debug:
            print("Tetris AI 初始化完成 - 调试模式开启")

    def log_debug(self, message):
        """调试日志输出"""
        if self.debug:
            timestamp = time.strftime("%H:%M:%S")
            print(f"[{timestamp}] {message}")

    def update_stats(self, stat_name, value=1):
        """更新统计信息"""
        if stat_name in self.stats:
            self.stats[stat_name] += value

    def print_stats(self):
        """打印统计信息"""
        if not self.debug:
            return

        print("\n=== 游戏统计 ===")
        for key, value in self.stats.items():
            print(f"{key}: {value}")

        if self.frame_times:
            avg_frame_time = sum(self.frame_times) / len(self.frame_times)
            print(f"平均帧处理时间: {avg_frame_time:.3f}秒")

        if self.decision_times:
            avg_decision_time = sum(self.decision_times) / len(self.decision_times)
            print(f"平均决策时间: {avg_decision_time:.3f}秒")
        print("================\n")

    def visualize_board(self, board, title="游戏板状态"):
        """可视化游戏板（控制台输出）"""
        if not self.debug:
            return

        print(f"\n--- {title} ---")
        for row in board:
            line = ""
            for cell in row:
                line += "█" if cell == 1 else "·"
            print(line)
        print("-" * len(board[0]))

    def save_debug_image(self, frame, filename_prefix="debug"):
        """保存调试图像"""
        if not self.debug or frame is None:
            return

        try:
            timestamp = int(time.time())
            filename = f"{filename_prefix}_{timestamp}.png"
            cv2.imwrite(filename, frame)
            self.log_debug(f"调试图像已保存: {filename}")
        except Exception as e:
            self.log_debug(f"保存调试图像失败: {e}")

    def capture_screen(self):
        """优化的屏幕捕获函数，支持多种截图方式"""
        try:
            # 方法1: 使用macOS系统命令截图（推荐）
            return self._capture_with_screencapture()
        except Exception as e:
            print(f"系统截图失败: {e}")
            try:
                # 方法2: 使用pyautogui截图作为备选
                return self._capture_with_pyautogui()
            except Exception as e2:
                print(f"pyautogui截图也失败: {e2}")
                raise Exception("所有截图方法都失败了")

    def _capture_with_screencapture(self):
        """使用macOS系统命令截图"""
        import subprocess
        import tempfile
        import os

        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
        temp_file.close()

        try:
            # 使用系统命令截图
            x, y, w, h = self.game_region
            subprocess.run([
                'screencapture', '-R{},{},{},{}'.format(x, y, w, h),
                temp_file.name
            ], check=True, timeout=5)

            # 读取截图文件
            frame = cv2.imread(temp_file.name)
            if frame is None:
                raise Exception("截图文件读取失败")

            return frame
        finally:
            # 确保删除临时文件
            try:
                os.unlink(temp_file.name)
            except:
                pass

    def _capture_with_pyautogui(self):
        """使用pyautogui截图作为备选方案"""
        screenshot = pyautogui.screenshot()
        frame = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

        # 安全裁剪游戏区域
        x, y, w, h = self.game_region
        frame_height, frame_width = frame.shape[:2]

        # 确保裁剪区域在屏幕范围内
        x = max(0, min(x, frame_width))
        y = max(0, min(y, frame_height))
        x_end = min(x + w, frame_width)
        y_end = min(y + h, frame_height)

        if x >= x_end or y >= y_end:
            raise Exception("游戏区域超出屏幕范围")

        return frame[y:y_end, x:x_end]

    def detect_game_start(self, frame):
        """改进的游戏开始界面检测"""
        if frame is None or frame.size == 0:
            return False, None

        try:
            # 多种检测方法组合
            methods = [
                self._detect_start_by_color,
                self._detect_start_by_text,
                self._detect_start_by_pattern
            ]

            for method in methods:
                found, pos = method(frame)
                if found:
                    return True, pos

            return False, None
        except Exception as e:
            print(f"游戏开始检测出错: {e}")
            return False, None

    def _detect_start_by_color(self, frame):
        """通过颜色检测开始按钮"""
        try:
            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)

            # 检测多种可能的按钮颜色
            color_ranges = [
                ([35, 50, 50], [85, 255, 255]),    # 绿色
                ([100, 50, 50], [130, 255, 255]),  # 蓝色
                ([0, 50, 50], [20, 255, 255]),     # 红色
            ]

            for lower, upper in color_ranges:
                mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                for contour in contours:
                    area = cv2.contourArea(contour)
                    if 100 < area < 5000:  # 合理的按钮大小范围
                        x, y, w, h = cv2.boundingRect(contour)
                        # 检查长宽比是否合理（按钮通常不会太细长）
                        aspect_ratio = w / h if h > 0 else 0
                        if 0.5 < aspect_ratio < 3.0:
                            center_x = x + w // 2
                            center_y = y + h // 2
                            return True, (center_x, center_y)

            return False, None
        except Exception:
            return False, None

    def _detect_start_by_text(self, frame):
        """通过文字检测开始界面"""
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            # 使用多个阈值尝试
            thresholds = [100, 127, 150, 180]

            for thresh_val in thresholds:
                _, thresh = cv2.threshold(gray, thresh_val, 255, cv2.THRESH_BINARY)
                contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                # 寻找可能的文字区域
                text_contours = []
                for contour in contours:
                    area = cv2.contourArea(contour)
                    if 50 < area < 2000:
                        x, y, w, h = cv2.boundingRect(contour)
                        aspect_ratio = w / h if h > 0 else 0
                        if 0.2 < aspect_ratio < 5.0:  # 文字的长宽比范围
                            text_contours.append(contour)

                # 如果找到足够多的文字轮廓，可能是开始界面
                if len(text_contours) > 5:
                    # 返回屏幕中心作为点击位置
                    h, w = frame.shape[:2]
                    return True, (w // 2, h // 2)

            return False, None
        except Exception:
            return False, None

    def _detect_start_by_pattern(self, frame):
        """通过图案检测开始界面"""
        try:
            # 检测是否有典型的游戏开始界面特征
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            # 计算图像的方差，开始界面通常有较高的方差
            variance = cv2.Laplacian(gray, cv2.CV_64F).var()

            # 检查是否有明显的边缘和结构
            edges = cv2.Canny(gray, 50, 150)
            edge_density = np.sum(edges > 0) / edges.size

            # 如果方差和边缘密度都在合理范围内，可能是开始界面
            if variance > 100 and 0.01 < edge_density < 0.3:
                h, w = frame.shape[:2]
                return True, (w // 2, h // 2)

            return False, None
        except Exception:
            return False, None

    def detect_current_piece(self, frame):
        """改进的当前方块检测"""
        if frame is None or frame.size == 0:
            return None, 0, 0

        try:
            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)

            # 优化的颜色范围，基于实际游戏测试
            color_ranges = {
                'I': ([85, 80, 80], [95, 255, 255]),    # 青色 - 收紧范围
                'O': ([22, 80, 80], [28, 255, 255]),    # 黄色
                'T': ([145, 80, 80], [155, 255, 255]),  # 紫色
                'L': ([12, 80, 80], [18, 255, 255]),    # 橙色
                'J': ([115, 80, 80], [125, 255, 255]),  # 蓝色
                'S': ([45, 80, 80], [55, 255, 255]),    # 绿色
                'Z': ([2, 80, 80], [8, 255, 255])       # 红色
            }

            # 检测多个区域，不仅仅是顶部
            regions = [
                hsv[0:80, :],      # 顶部区域
                hsv[20:120, :],    # 上部区域
                hsv[40:140, :]     # 中上部区域
            ]

            best_piece = None
            best_confidence = 0
            best_x, best_y = 0, 0

            for region_idx, region in enumerate(regions):
                for color_name, (lower, upper) in color_ranges.items():
                    lower = np.array(lower)
                    upper = np.array(upper)
                    mask = cv2.inRange(region, lower, upper)

                    # 形态学操作去除噪点
                    kernel = np.ones((3,3), np.uint8)
                    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
                    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)

                    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                    for contour in contours:
                        area = cv2.contourArea(contour)
                        if area > 50:  # 提高面积阈值
                            x, y, w, h = cv2.boundingRect(contour)

                            # 计算置信度（基于面积和形状）
                            aspect_ratio = w / h if h > 0 else 0
                            confidence = area * (1 - abs(aspect_ratio - 1))  # 偏好正方形

                            # 顶部区域的方块优先级更高
                            if region_idx == 0:
                                confidence *= 1.5

                            if confidence > best_confidence:
                                best_confidence = confidence
                                best_piece = color_name

                                # 计算方块中心位置（相对于整个frame）
                                center_x = x + w // 2
                                center_y = y + h // 2 + region_idx * 20  # 调整y坐标

                                # 转换为游戏板坐标
                                cell_width = frame.shape[1] // 10
                                cell_height = frame.shape[0] // 20
                                best_x = center_x // cell_width
                                best_y = center_y // cell_height

            return best_piece, best_x, best_y

        except Exception as e:
            print(f"方块检测出错: {e}")
            return None, 0, 0

    def detect_blocks(self, frame):
        """检测当前方块和游戏区域"""
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        board = np.zeros((20, 10), dtype=int)  # 20行10列的游戏板

        # 优化颜色检测阈值
        color_ranges = {
            'I': ([75, 100, 100], [105, 255, 255]),   # 青色（扩大范围）
            'O': ([15, 100, 100], [35, 255, 255]),    # 黄色
            'T': ([130, 100, 100], [170, 255, 255]),  # 紫色
            'L': ([5, 100, 100], [25, 255, 255]),     # 橙色
            'J': ([100, 100, 100], [140, 255, 255]),  # 蓝色
            'S': ([35, 100, 100], [65, 255, 255]),    # 绿色
            'Z': ([0, 100, 100], [15, 255, 255])      # 红色
        }

        # 检测当前活动方块
        self.current_piece, self.current_x, self.current_y = self.detect_current_piece(frame)

        for color_name, (lower, upper) in color_ranges.items():
            lower = np.array(lower)
            upper = np.array(upper)
            mask = cv2.inRange(hsv, lower, upper)
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            for contour in contours:
                if cv2.contourArea(contour) > 80:  # 增加面积阈值减少误检
                    x, y, w, h = cv2.boundingRect(contour)
                    # 转换为游戏板坐标
                    cell_width = self.game_region[2] // 10
                    cell_height = self.game_region[3] // 20
                    board_y = y // cell_height
                    board_x = x // cell_width
                    if 0 <= board_y < 20 and 0 <= board_x < 10:
                        board[board_y, board_x] = 1

        return board

    def rotate_piece(self, piece):
        """仅顺时针旋转方块"""
        return np.rot90(piece, k=-1).tolist()  # 仅保留顺时针旋转

    def check_collision(self, board, piece, x, y):
        """检查方块是否碰撞"""
        for dy, row in enumerate(piece):
            for dx, cell in enumerate(row):
                if cell:
                    ny = y + dy
                    nx = x + dx
                    if ny >= 20 or nx < 0 or nx >= 10 or (ny >= 0 and board[ny, nx]):
                        return True
        return False

    def simulate_move(self, board, piece_type, x, y, rotation_count):
        """模拟方块放置，仅支持顺时针旋转"""
        piece = self.pieces[piece_type]
        # 应用旋转
        for _ in range(rotation_count):
            piece = self.rotate_piece(piece)

        # 找到最终落点
        while not self.check_collision(board, piece, x, y+1):
            y += 1

        # 创建模拟棋盘
        simulated_board = board.copy()
        for dy, row in enumerate(piece):
            for dx, cell in enumerate(row):
                if cell and 0 <= y+dy < 20 and 0 <= x+dx < 10:
                    simulated_board[y+dy, x+dx] = 1

        # 消除已满行
        lines_cleared = 0
        new_board = []
        for row in simulated_board:
            if np.all(row == 1):
                lines_cleared += 1
            else:
                new_board.append(row)

        # 添加新的空行
        for _ in range(lines_cleared):
            new_board.insert(0, np.zeros(10, dtype=int))

        return np.array(new_board), lines_cleared

    def evaluate_board(self, board):
        """改进的评估函数：更全面的评估标准"""
        try:
            # 基础指标
            lines_cleared = self.count_lines_cleared(board)
            height = self.get_board_height(board)
            holes = self.count_holes(board)
            bumpiness = self.calculate_bumpiness(board)

            # 高级指标
            wells = self.count_wells(board)
            blocked_cells = self.count_blocked_cells(board)
            row_transitions = self.count_row_transitions(board)
            col_transitions = self.count_col_transitions(board)

            # 动态权重调整
            height_penalty = height * (8 + height * 0.5)  # 高度越高惩罚越重

            # 综合评分
            score = (
                lines_cleared * 200 +           # 消行奖励
                -height_penalty +               # 高度惩罚
                -holes * 35 +                   # 洞穴惩罚
                -bumpiness * 6 +                # 不平整惩罚
                -wells * 15 +                   # 深井惩罚
                -blocked_cells * 25 +           # 被阻挡格子惩罚
                -row_transitions * 2 +          # 行转换惩罚
                -col_transitions * 2            # 列转换惩罚
            )

            # 特殊情况奖励
            if lines_cleared >= 4:  # Tetris奖励
                score += 100
            elif lines_cleared >= 2:  # 多行消除奖励
                score += 50

            return score

        except Exception as e:
            print(f"评估函数出错: {e}")
            return -1000  # 出错时返回很低的分数

    def count_lines_cleared(self, board):
        return np.sum(np.all(board == 1, axis=1))

    def get_board_height(self, board):
        # 计算当前堆积高度（从底部到最高方块的行数）
        for i in range(len(board)):
            if any(cell == 1 for cell in board[i]):
                return len(board) - i
        return 0

    def count_holes(self, board):
        holes = 0
        for col in range(board.shape[1]):
            block_found = False
            for row in range(board.shape[0]):
                if board[row, col] == 1:
                    block_found = True
                elif block_found and board[row, col] == 0:
                    holes += 1
        return holes

    def calculate_bumpiness(self, board):
        """计算列高度差（平滑度）"""
        heights = self.get_column_heights(board)
        bumpiness = 0
        for i in range(1, len(heights)):
            bumpiness += abs(heights[i] - heights[i-1])
        return bumpiness

    def get_column_heights(self, board):
        """获取每列的高度"""
        heights = []
        for col in range(board.shape[1]):
            for row in range(board.shape[0]):
                if board[row, col] == 1:
                    heights.append(board.shape[0] - row)
                    break
            else:
                heights.append(0)
        return heights

    def count_wells(self, board):
        """计算深井数量（被两边包围的空列）"""
        heights = self.get_column_heights(board)
        wells = 0

        for col in range(len(heights)):
            left_higher = col == 0 or heights[col-1] > heights[col]
            right_higher = col == len(heights)-1 or heights[col+1] > heights[col]

            if left_higher and right_higher:
                # 计算井的深度
                well_depth = 0
                if col > 0 and col < len(heights)-1:
                    well_depth = min(heights[col-1], heights[col+1]) - heights[col]
                elif col == 0:
                    well_depth = heights[col+1] - heights[col]
                elif col == len(heights)-1:
                    well_depth = heights[col-1] - heights[col]

                wells += max(0, well_depth)

        return wells

    def count_blocked_cells(self, board):
        """计算被上方方块阻挡的空格数量"""
        blocked = 0
        for col in range(board.shape[1]):
            block_found = False
            for row in range(board.shape[0]):
                if board[row, col] == 1:
                    block_found = True
                elif block_found and board[row, col] == 0:
                    blocked += 1
        return blocked

    def count_row_transitions(self, board):
        """计算行转换数量（相邻格子状态不同的次数）"""
        transitions = 0
        for row in range(board.shape[0]):
            for col in range(board.shape[1] - 1):
                if board[row, col] != board[row, col + 1]:
                    transitions += 1
        return transitions

    def count_col_transitions(self, board):
        """计算列转换数量"""
        transitions = 0
        for col in range(board.shape[1]):
            for row in range(board.shape[0] - 1):
                if board[row, col] != board[row + 1, col]:
                    transitions += 1
        return transitions

    def get_board(self, frame):
        # 从游戏画面提取面板数据（10列x20行标准俄罗斯方块面板）
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        _, thresh = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY_INV)
        
        # 计算单元格尺寸（假设游戏区域已正确裁剪）
        cell_width = frame.shape[1] // 10
        cell_height = frame.shape[0] // 20
        
        board = []
        for y in range(20):
            row = []
            for x in range(10):
                # 检查单元格是否有方块填充
                cell = thresh[y*cell_height:(y+1)*cell_height, x*cell_width:(x+1)*cell_width]
                filled = cv2.countNonZero(cell) > 50  # 调整填充判断阈值
                row.append(1 if filled else 0)
            board.append(row)
        return board

    def get_best_move(self, board):
        """优化的移动决策算法，仅考虑顺时针旋转"""
        if not self.current_piece:
            return ['down']  # 返回动作列表而非字符串

        best_score = -np.inf
        best_actions = []
        piece_type = self.current_piece
        original_x = self.current_x
        original_y = self.current_y

        # 仅尝试顺时针旋转
        for rotation_count in range(4):
            # 尝试所有可能的水平位置
            for dx in range(-5, 6):  # 扩大水平搜索范围
                x = original_x + dx
                # 模拟移动
                simulated_board, lines_cleared = self.simulate_move(board, piece_type, x, original_y, rotation_count)
                # 评估分数
                score = self.evaluate_board(simulated_board)
                if lines_cleared > 0:
                    score += lines_cleared * 50

                if score > best_score:
                    best_score = score
                    best_actions = []
                    # 添加旋转动作
                    for _ in range(rotation_count):
                        best_actions.append('rotate')
                    # 添加水平移动动作
                    if dx < 0:
                        best_actions.extend(['left'] * abs(dx))
                    elif dx > 0:
                        best_actions.extend(['right'] * dx)
                    # 添加硬下落
                    best_actions.append('hard_drop')

        # 返回动作序列
        return best_actions if best_actions else ['hard_drop']  # 确保返回默认动作

    def execute_action(self, action, button_pos=None):
        """改进的动作执行函数"""
        current_time = time.time()
        if current_time - self.last_action_time < self.action_delay:
            return False  # 返回执行状态

        try:
            self.last_action_time = current_time

            if action == 'left':
                pyautogui.press('left')
            elif action == 'right':
                pyautogui.press('right')
            elif action == 'down':
                pyautogui.press('down')
            elif action == 'rotate':
                pyautogui.press('up')  # 或者使用 'x' 键
            elif action == 'hard_drop':
                pyautogui.press('space')
            elif action == 'start':
                if button_pos:
                    # 鼠标点击开始按钮
                    pyautogui.moveTo(button_pos[0], button_pos[1], duration=0.3)
                    pyautogui.click()
                else:
                    # 使用键盘启动
                    pyautogui.press('enter')

            return True

        except Exception as e:
            print(f"动作执行失败 {action}: {e}")
            return False

    def run(self):
        """改进的主运行循环"""
        self.log_debug("Tetris AI 启动中...")
        self.log_debug("按Ctrl+C停止")

        # 激活模拟器窗口
        try:
            import os
            os.system('osascript -e \'tell application "SameBoy" to activate\'')
            time.sleep(3)
        except Exception as e:
            self.log_debug(f"激活窗口失败: {e}")

        try:
            frame_count = 0
            last_stats_time = time.time()

            while True:
                frame_start_time = time.time()

                # 捕获屏幕
                try:
                    frame = self.capture_screen()
                    if frame is None:
                        self.log_debug("截图失败，跳过此帧")
                        time.sleep(0.1)
                        continue
                except Exception as e:
                    self.log_debug(f"截图异常: {e}")
                    self.update_stats('detection_errors')
                    time.sleep(0.5)
                    continue

                frame_count += 1

                # 游戏状态检测和处理
                if not self.game_started:
                    self._handle_game_start(frame)
                else:
                    self._handle_game_play(frame)

                # 性能统计
                frame_time = time.time() - frame_start_time
                self.frame_times.append(frame_time)
                if len(self.frame_times) > 100:  # 只保留最近100帧的数据
                    self.frame_times.pop(0)

                # 定期打印统计信息
                if time.time() - last_stats_time > 30:  # 每30秒打印一次
                    self.print_stats()
                    last_stats_time = time.time()

                # 检查游戏结束条件
                if self.game_started:
                    if time.time() - self.start_time > self.game_duration:
                        self.log_debug("游戏时间结束")
                        break
                    if self.detect_game_over(frame):
                        self.log_debug("检测到游戏结束")
                        break

                # 控制帧率
                time.sleep(0.05)  # 20 FPS

        except KeyboardInterrupt:
            self.log_debug("用户中断，AI停止")
        except Exception as e:
            self.log_debug(f"运行时异常: {e}")
        finally:
            self.print_stats()
            self.log_debug("Tetris AI 已停止")

    def _handle_game_start(self, frame):
        """处理游戏开始阶段"""
        game_started, button_pos = self.detect_game_start(frame)

        if game_started:
            self.log_debug("检测到游戏开始界面")
            if self.execute_action('start', button_pos):
                self.log_debug("发送启动指令成功")
                time.sleep(2)
                self.game_started = True
                self.start_time = time.time()
            else:
                self.log_debug("发送启动指令失败")
        else:
            # 保存调试图像（偶尔）
            if time.time() % 10 < 0.1:  # 大约每10秒保存一次
                self.save_debug_image(frame, "waiting_for_start")

    def _handle_game_play(self, frame):
        """处理游戏进行阶段"""
        decision_start_time = time.time()

        try:
            # 检测游戏状态
            board = self.detect_blocks(frame)

            # 执行动作队列
            if self.action_queue:
                action = self.action_queue.pop(0)
                if self.execute_action(action):
                    self.update_stats('actions_executed')
                    self.log_debug(f"执行动作: {action}")
            else:
                # 生成新的动作序列
                actions = self.get_best_move(board)
                if actions:
                    self.action_queue = actions
                    self.log_debug(f"生成动作序列: {actions}")
                    self.update_stats('pieces_placed')

            # 记录决策时间
            decision_time = time.time() - decision_start_time
            self.decision_times.append(decision_time)
            if len(self.decision_times) > 50:
                self.decision_times.pop(0)

            # 可视化（调试模式）
            if self.debug and len(board) > 0:
                self.visualize_board(board)

        except Exception as e:
            self.log_debug(f"游戏处理异常: {e}")
            self.update_stats('detection_errors')

    def detect_game_over(self, frame):
        """改进的游戏结束检测"""
        if frame is None or frame.size == 0:
            return False

        try:
            # 多种检测方法
            methods = [
                self._detect_game_over_by_color,
                self._detect_game_over_by_text,
                self._detect_game_over_by_stillness
            ]

            for method in methods:
                if method(frame):
                    return True

            return False
        except Exception as e:
            print(f"游戏结束检测出错: {e}")
            return False

    def _detect_game_over_by_color(self, frame):
        """通过颜色检测游戏结束"""
        try:
            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)

            # 检测红色文字（GAME OVER通常是红色）
            red_ranges = [
                ([0, 100, 100], [10, 255, 255]),
                ([170, 100, 100], [180, 255, 255])
            ]

            total_red_area = 0
            for lower, upper in red_ranges:
                mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                for contour in contours:
                    area = cv2.contourArea(contour)
                    if area > 20:  # 过滤小噪点
                        total_red_area += area

            # 如果红色区域足够大，可能是游戏结束文字
            return total_red_area > 200
        except Exception:
            return False

    def _detect_game_over_by_text(self, frame):
        """通过文字特征检测游戏结束"""
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            # 在中央区域寻找文字
            h, w = gray.shape
            center_region = gray[h//4:3*h//4, w//4:3*w//4]

            # 使用多个阈值
            for thresh_val in [100, 127, 150, 200]:
                _, thresh = cv2.threshold(center_region, thresh_val, 255, cv2.THRESH_BINARY)
                contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                # 统计可能的文字轮廓
                text_like_contours = 0
                for contour in contours:
                    area = cv2.contourArea(contour)
                    if 30 < area < 1000:
                        x, y, w, h = cv2.boundingRect(contour)
                        aspect_ratio = w / h if h > 0 else 0
                        if 0.3 < aspect_ratio < 3.0:
                            text_like_contours += 1

                # 如果找到足够多的文字轮廓，可能是游戏结束界面
                if text_like_contours >= 8:  # GAME OVER 大约8-10个字符
                    return True

            return False
        except Exception:
            return False

    def _detect_game_over_by_stillness(self, frame):
        """通过画面静止检测游戏结束"""
        try:
            # 这个方法需要保存前一帧进行比较
            if not hasattr(self, 'previous_frame'):
                self.previous_frame = frame.copy()
                return False

            # 计算当前帧与前一帧的差异
            diff = cv2.absdiff(frame, self.previous_frame)
            gray_diff = cv2.cvtColor(diff, cv2.COLOR_BGR2GRAY)

            # 计算变化程度
            change_ratio = np.sum(gray_diff > 30) / gray_diff.size

            # 更新前一帧
            self.previous_frame = frame.copy()

            # 如果画面几乎没有变化，可能是游戏结束
            return change_ratio < 0.01
        except Exception:
            return False

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='Tetris AI for Game Boy Color')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--duration', type=int, default=300, help='游戏时长（秒）')
    parser.add_argument('--region', nargs=4, type=int, metavar=('X', 'Y', 'W', 'H'),
                       help='游戏窗口区域 (x, y, width, height)')

    args = parser.parse_args()

    # 创建AI实例
    ai = TetrisAI(debug=args.debug)

    # 设置游戏时长
    if args.duration:
        ai.game_duration = args.duration

    # 设置游戏区域
    if args.region:
        ai.game_region = tuple(args.region)
        print(f"使用自定义游戏区域: {ai.game_region}")

    print("=== Tetris AI for Game Boy Color ===")
    print("使用说明:")
    print("1. 确保SameBoy模拟器已打开并加载俄罗斯方块游戏")
    print("2. 将游戏窗口放在屏幕左上角")
    print("3. 按Ctrl+C可随时停止AI")
    print("4. AI会自动检测游戏开始界面并开始游戏")
    print("=====================================\n")

    try:
        ai.run()
    except Exception as e:
        print(f"运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()