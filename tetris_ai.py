import pyautogui
import cv2
import numpy as np
import time
from PIL import ImageGrab

class TetrisAI:
    def __init__(self):
        # 校准游戏窗口区域（根据实际游戏窗口调整）
        self.game_region = (0, 80, 660, 940)  # 扩大游戏区域以确保完整捕获
        self.block_colors = {
            'I': ([70, 80, 80], [110, 255, 255]),   # 青色（扩大HSV范围）
            'O': ([15, 80, 80], [35, 255, 255]),    # 黄色
            'T': ([130, 80, 80], [170, 255, 255]),  # 紫色
            'L': ([5, 80, 80], [25, 255, 255]),     # 橙色
            'J': ([100, 80, 80], [140, 255, 255]),  # 蓝色
            'S': ([35, 80, 80], [65, 255, 255]),    # 绿色
            'Z': ([0, 80, 80], [20, 255, 255])      # 红色
        }
        # 方块形状定义 (I, <PERSON>, <PERSON>, L, J, S, Z)
        self.pieces = {
            'I': [[1, 1, 1, 1]],
            'O': [[1, 1], [1, 1]],
            'T': [[0, 1, 0], [1, 1, 1]],
            'L': [[0, 0, 1], [1, 1, 1]],
            'J': [[1, 0, 0], [1, 1, 1]],
            'S': [[0, 1, 1], [1, 1, 0]],
            'Z': [[1, 1, 0], [0, 1, 1]]
        }
        self.last_action_time = 0
        self.action_delay = 0.2  # 增加操作延迟至0.2秒
        self.game_started = False
        self.start_time = 0
        self.game_duration = 180  # 3分钟游戏时间（秒）
        self.current_piece = None
        self.current_x = 0
        self.current_y = 0
        self.current_rotation = 0
        self.action_queue = []  # 动作队列

    def capture_screen(self):
        # 使用macOS系统命令截图，避开pyautogui依赖问题
        import subprocess
        import tempfile
        import os
        
        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
        temp_file.close()
        
        # 使用系统命令截图
        x, y, w, h = self.game_region
        subprocess.run([
            'screencapture', '-R{},{},{},{}'.format(x, y, w, h),
            temp_file.name
        ], check=True)
        
        # 读取截图文件
        frame = cv2.imread(temp_file.name)
        
        # 删除临时文件
        os.unlink(temp_file.name)
        
        return frame
        # 彻底移除region参数，确保兼容性
        screenshot = pyautogui.screenshot()
        frame = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        # 安全裁剪游戏区域
        x, y, w, h = self.game_region
        # 确保裁剪区域在屏幕范围内
        h, w_frame = frame.shape[:2]
        y_end = min(y + h, h_frame)
        x_end = min(x + w, w_frame)
        frame = frame[y:y_end, x:x_end]
        return frame

    def detect_game_start(self, frame):
        """检测游戏开始界面，返回是否找到开始按钮及按钮坐标"""
        # 先裁剪游戏区域
        game_frame = frame[self.game_region[1]:self.game_region[1]+self.game_region[3],
                          self.game_region[0]:self.game_region[0]+self.game_region[2]]
        # 转换为HSV颜色空间检测绿色按钮
        hsv = cv2.cvtColor(game_frame, cv2.COLOR_BGR2HSV)
        # 绿色按钮HSV范围（超宽范围确保检测）
        lower_green = np.array([15, 15, 15])
        upper_green = np.array([110, 255, 255])
        mask = cv2.inRange(hsv, lower_green, upper_green)
        # 查找绿色区域轮廓
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        # 进一步降低面积阈值
        for contour in contours:
            if cv2.contourArea(contour) > 30:
                # 计算按钮中心坐标（相对于游戏区域）
                x, y, w, h = cv2.boundingRect(contour)
                btn_center_x = self.game_region[0] + x + w//2
                btn_center_y = self.game_region[1] + y + h//2
                return True, (btn_center_x, btn_center_y)
        return False, None
        # 检测游戏标题画面的特定区域（相对于游戏区域的坐标）
        start_button_region = game_frame[300:380, 150:350]  # 相对于裁剪后的游戏区域
        gray = cv2.cvtColor(start_button_region, cv2.COLOR_BGR2GRAY)
        _, thresh = cv2.threshold(gray, 140, 255, cv2.THRESH_BINARY)  # 进一步降低阈值提高灵敏度
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        return len(contours) > 0  # 只要有轮廓就认为检测到按钮

    def detect_current_piece(self, frame):
        """检测当前活动方块类型和位置"""
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        color_ranges = {
            'I': ([80, 100, 100], [100, 255, 255]),
            'O': ([20, 100, 100], [30, 255, 255]),
            'T': ([140, 100, 100], [160, 255, 255]),
            'L': ([10, 100, 100], [20, 255, 255]),
            'J': ([110, 100, 100], [130, 255, 255]),
            'S': ([40, 100, 100], [60, 255, 255]),
            'Z': ([0, 100, 100], [20, 255, 255])
        }

        # 检测顶部区域的活动方块
        top_region = hsv[0:100, :]  # 游戏区域顶部
        for color_name, (lower, upper) in color_ranges.items():
            lower = np.array(lower)
            upper = np.array(upper)
            mask = cv2.inRange(top_region, lower, upper)
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            for contour in contours:
                if cv2.contourArea(contour) > 30:  # 降低轮廓面积阈值至30
                    x, y, w, h = cv2.boundingRect(contour)
                    # 计算方块中心位置
                    center_x = x + w // 2
                    center_y = y + h // 2
                    cell_width = self.game_region[2] // 10
                    cell_height = self.game_region[3] // 20
                    board_x = center_x // cell_width
                    board_y = center_y // cell_height
                    return color_name, board_x, board_y
        return None, 0, 0

    def detect_blocks(self, frame):
        """检测当前方块和游戏区域"""
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        board = np.zeros((20, 10), dtype=int)  # 20行10列的游戏板

        # 优化颜色检测阈值
        color_ranges = {
            'I': ([75, 100, 100], [105, 255, 255]),   # 青色（扩大范围）
            'O': ([15, 100, 100], [35, 255, 255]),    # 黄色
            'T': ([130, 100, 100], [170, 255, 255]),  # 紫色
            'L': ([5, 100, 100], [25, 255, 255]),     # 橙色
            'J': ([100, 100, 100], [140, 255, 255]),  # 蓝色
            'S': ([35, 100, 100], [65, 255, 255]),    # 绿色
            'Z': ([0, 100, 100], [15, 255, 255])      # 红色
        }

        # 检测当前活动方块
        self.current_piece, self.current_x, self.current_y = self.detect_current_piece(frame)

        for color_name, (lower, upper) in color_ranges.items():
            lower = np.array(lower)
            upper = np.array(upper)
            mask = cv2.inRange(hsv, lower, upper)
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            for contour in contours:
                if cv2.contourArea(contour) > 80:  # 增加面积阈值减少误检
                    x, y, w, h = cv2.boundingRect(contour)
                    # 转换为游戏板坐标
                    cell_width = self.game_region[2] // 10
                    cell_height = self.game_region[3] // 20
                    board_y = y // cell_height
                    board_x = x // cell_width
                    if 0 <= board_y < 20 and 0 <= board_x < 10:
                        board[board_y, board_x] = 1

        return board

    def rotate_piece(self, piece):
        """仅顺时针旋转方块"""
        return np.rot90(piece, k=-1).tolist()  # 仅保留顺时针旋转

    def check_collision(self, board, piece, x, y):
        """检查方块是否碰撞"""
        for dy, row in enumerate(piece):
            for dx, cell in enumerate(row):
                if cell:
                    ny = y + dy
                    nx = x + dx
                    if ny >= 20 or nx < 0 or nx >= 10 or (ny >= 0 and board[ny, nx]):
                        return True
        return False

    def simulate_move(self, board, piece_type, x, y, rotation_count):
        """模拟方块放置，仅支持顺时针旋转"""
        piece = self.pieces[piece_type]
        # 应用旋转
        for _ in range(rotation_count):
            piece = self.rotate_piece(piece)

        # 找到最终落点
        while not self.check_collision(board, piece, x, y+1):
            y += 1

        # 创建模拟棋盘
        simulated_board = board.copy()
        for dy, row in enumerate(piece):
            for dx, cell in enumerate(row):
                if cell and 0 <= y+dy < 20 and 0 <= x+dx < 10:
                    simulated_board[y+dy, x+dx] = 1

        # 消除已满行
        lines_cleared = 0
        new_board = []
        for row in simulated_board:
            if np.all(row == 1):
                lines_cleared += 1
            else:
                new_board.append(row)

        # 添加新的空行
        for _ in range(lines_cleared):
            new_board.insert(0, np.zeros(10, dtype=int))

        return np.array(new_board), lines_cleared

    def evaluate_board(self, board):
        """高级评估函数：综合考虑多种因素"""
        lines_cleared = self.count_lines_cleared(board)
        height = self.get_board_height(board)
        holes = self.count_holes(board)
        bumpiness = self.calculate_bumpiness(board)

        # 优化权重以适应难度1
        score = lines_cleared * 150 - height * 8 - holes * 30 - bumpiness * 5
        return score

    def count_lines_cleared(self, board):
        return np.sum(np.all(board == 1, axis=1))

    def get_board_height(self, board):
        # 计算当前堆积高度（从底部到最高方块的行数）
        for i in range(len(board)):
            if any(cell == 1 for cell in board[i]):
                return len(board) - i
        return 0

    def count_holes(self, board):
        holes = 0
        for col in range(board.shape[1]):
            block_found = False
            for row in range(board.shape[0]):
                if board[row, col] == 1:
                    block_found = True
                elif block_found and board[row, col] == 0:
                    holes += 1
        return holes

    def calculate_bumpiness(self, board):
        """计算列高度差（平滑度）"""
        heights = []
        for col in range(board.shape[1]):
            for row in range(board.shape[0]):
                if board[row, col] == 1:
                    heights.append(board.shape[0] - row)
                    break
            else:
                heights.append(0)

        bumpiness = 0
        for i in range(1, len(heights)):
            bumpiness += abs(heights[i] - heights[i-1])
        return bumpiness

    def get_board(self, frame):
        # 从游戏画面提取面板数据（10列x20行标准俄罗斯方块面板）
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        _, thresh = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY_INV)
        
        # 计算单元格尺寸（假设游戏区域已正确裁剪）
        cell_width = frame.shape[1] // 10
        cell_height = frame.shape[0] // 20
        
        board = []
        for y in range(20):
            row = []
            for x in range(10):
                # 检查单元格是否有方块填充
                cell = thresh[y*cell_height:(y+1)*cell_height, x*cell_width:(x+1)*cell_width]
                filled = cv2.countNonZero(cell) > 50  # 调整填充判断阈值
                row.append(1 if filled else 0)
            board.append(row)
        return board

    def get_best_move(self, board):
        """优化的移动决策算法，仅考虑顺时针旋转"""
        if not self.current_piece:
            return ['down']  # 返回动作列表而非字符串

        best_score = -np.inf
        best_actions = []
        piece_type = self.current_piece
        original_x = self.current_x
        original_y = self.current_y

        # 仅尝试顺时针旋转
        for rotation_count in range(4):
            # 尝试所有可能的水平位置
            for dx in range(-5, 6):  # 扩大水平搜索范围
                x = original_x + dx
                # 模拟移动
                simulated_board, lines_cleared = self.simulate_move(board, piece_type, x, original_y, rotation_count)
                # 评估分数
                score = self.evaluate_board(simulated_board)
                if lines_cleared > 0:
                    score += lines_cleared * 50

                if score > best_score:
                    best_score = score
                    best_actions = []
                    # 添加旋转动作
                    for _ in range(rotation_count):
                        best_actions.append('rotate')
                    # 添加水平移动动作
                    if dx < 0:
                        best_actions.extend(['left'] * abs(dx))
                    elif dx > 0:
                        best_actions.extend(['right'] * dx)
                    # 添加硬下落
                    best_actions.append('hard_drop')

        # 返回动作序列
        return best_actions if best_actions else ['hard_drop']  # 确保返回默认动作

    def execute_action(self, action, button_pos=None):
        current_time = time.time()
        if current_time - self.last_action_time < self.action_delay:
            return

        self.last_action_time = current_time
        if action == 'left':
            pyautogui.press('left')
        elif action == 'right':
            pyautogui.press('right')
        elif action == 'down':
            pyautogui.press('down')
        elif action == 'rotate':
            pyautogui.press('up')
        elif action == 'hard_drop':
            pyautogui.press('space')
        elif action == 'start' and button_pos:
            # 鼠标点击开始按钮
            pyautogui.moveTo(button_pos[0], button_pos[1], duration=0.5)
            pyautogui.click()
        elif action == 'rotate':
            pyautogui.press('x')     # X键旋转
        elif action == 'start':
            # 使用键盘回车启动游戏，提高可靠性
            pyautogui.press('enter')

    def run(self):
        print("Tetris AI 启动中...")
        print("按Ctrl+C停止")
        # 激活模拟器窗口
        import os
        os.system('osascript -e \'tell application "SameBoy" to activate\'')
        time.sleep(4)  # 进一步延长窗口激活等待时间
        try:
            while True:
                frame = self.capture_screen()
                
                # 检测游戏是否开始
                if not self.game_started:
                    game_started, button_pos = self.detect_game_start(frame)
                    print(f"游戏开始检测: {game_started}, 按钮位置: {button_pos}")  # 调试打印
                    if game_started:
                        print("检测到游戏开始界面，启动游戏...")
                        self.execute_action('start', button_pos)
                        time.sleep(3)  # 延长启动后等待时间确保游戏加载完成
                        print("已发送启动游戏指令，等待游戏加载...")  # 调试打印
                        self.game_started = True  # 设置游戏已启动标志
                        self.start_time = time.time()
                        time.sleep(5)  # 延长启动延迟至5秒
                        continue
                    else:
                        # 未检测到开始界面时等待
                        time.sleep(0.5)
                        continue
                
                # 游戏已开始，处理游戏逻辑
                board = self.detect_blocks(frame)
                if not self.action_queue:
                    self.action_queue = self.get_best_move(board) or ['hard_drop']
                else:
                    # 主游戏循环调试信息
                    print("进入主游戏循环，检测当前方块...")
                    # 获取游戏面板数据
                    board = self.get_board(frame)
                    current_piece = self.detect_current_piece(self.get_board_height(board), frame)
                    print(f"检测到方块类型: {current_piece}")
                    if current_piece:
                        best_move = self.get_best_move(current_piece)
                        print(f"执行最佳移动: {best_move}")
                        self.execute_action(best_move)
                    action = self.action_queue.pop(0)
                    self.execute_action(action)

                # 检查游戏是否结束或时间到
                if time.time() - self.start_time > self.game_duration:
                    print("游戏时间结束")
                    break
                if self.detect_game_over(frame):
                    print("检测到游戏结束画面")
                    break

                # 显示剩余时间
                remaining_time = max(0, self.game_duration - (time.time() - self.start_time))
                if int(remaining_time) % 10 == 0 and remaining_time > 0:
                    print(f"剩余时间: {int(remaining_time)}秒")
                
                time.sleep(0.1)
        except KeyboardInterrupt:
            print("AI已停止")

    def detect_game_over(self, frame):
        """检测游戏结束界面，使用红色文字颜色过滤提高准确性"""
        # 精确匹配游戏结束画面中央的红色"GAME OVER"文字区域
        game_over_region = frame[250:300, 180:320]  # 精确匹配文字位置
        hsv = cv2.cvtColor(game_over_region, cv2.COLOR_BGR2HSV)
        # 红色文字HSV范围（收紧范围减少误检）
        lower_red1 = np.array([0, 150, 150])
        upper_red1 = np.array([5, 255, 255])
        lower_red2 = np.array([175, 150, 150])
        upper_red2 = np.array([180, 255, 255])
        mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
        mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
        mask = cv2.bitwise_or(mask1, mask2)
        # 寻找红色文字轮廓
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        # 过滤小轮廓，仅保留较大的文字轮廓
        large_contours = [c for c in contours if cv2.contourArea(c) > 40]
        return len(large_contours) > 3  # 要求至少4个大轮廓（GAME OVER文字）

if __name__ == "__main__":
    ai = TetrisAI()
    ai.run()